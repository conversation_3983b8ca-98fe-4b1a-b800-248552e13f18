"""
Basic compute resource manager for satellite-cloud system
Provides resource management capabilities without offloading strategies
Offloading decisions should be made at agent level
"""

import numpy as np
from typing import List, Dict, Optional, Tuple, Any
import logging
import yaml
from pathlib import Path

# Absolute imports as per coding standard
from src.env.satellite_cloud.compute_models import (
    ComputeTask, TaskStatus, ProcessingResult, NodeType, 
    SatelliteState, CloudState, ResourceMetrics
)
from src.env.satellite_cloud.satellite_compute import SatelliteCompute
from src.env.satellite_cloud.cloud_compute import CloudCompute
from src.env.physics_layer.task_models import Task
from src.env.physics_layer.orbital import OrbitalUpdater
from src.env.physics_layer.communication_refactored import CommunicationManager
from src.env.Foundation_Layer.error_handling import handle_errors, SpaceSimulationError
from src.env.Foundation_Layer.logging_config import get_logger


class ComputeManager:
    """Basic compute resource manager without offloading strategies"""
    
    def __init__(self, config_path: str = None, config: Dict = None,
                 orbital_updater: Optional[OrbitalUpdater] = None,
                 comm_manager: Optional[CommunicationManager] = None):
        """
        Initialize compute manager
        
        Args:
            config_path: Path to configuration file
            config: Configuration dictionary (if config_path not provided)
            orbital_updater: Orbital updater instance
            comm_manager: Communication manager instance
        """
        # Load configuration - must be provided as per coding standard
        if config is not None:
            self.config = config
        elif config_path is not None:
            with open(config_path, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)
        else:
            raise ValueError("Either config_path or config must be provided")
        
        self.orbital_updater = orbital_updater
        self.comm_manager = comm_manager
        
        # Extract configuration parameters - no default values
        system_config = self.config['system']
        self.num_satellites = system_config['num_leo_satellites']
        self.num_clouds = system_config['num_cloud_centers']
        
        # Initialize compute nodes
        self.satellites: Dict[int, SatelliteCompute] = {}
        self.clouds: Dict[int, CloudCompute] = {}
        
        # Initialize satellites
        for sat_id in range(self.num_satellites):
            self.satellites[sat_id] = SatelliteCompute(
                satellite_id=sat_id,
                config=self.config,
                orbital_updater=orbital_updater,
                comm_manager=comm_manager
            )
        
        # Initialize cloud centers
        for cloud_id in range(self.num_clouds):
            self.clouds[cloud_id] = CloudCompute(
                cloud_id=cloud_id,
                config=self.config
            )
        
        # Performance tracking
        self.total_tasks_assigned = 0
        
        # Metrics tracking
        self.resource_metrics = ResourceMetrics()
        
        self.logger = get_logger("ComputeManager")
        
    @handle_errors(module="compute_manager", function="assign_task_to_satellite")
    def assign_task_to_satellite(self, task: Task, satellite_id: int) -> bool:
        """
        Assign task to specific satellite
        
        Args:
            task: Task to assign
            satellite_id: Target satellite ID
            
        Returns:
            True if successfully assigned, False otherwise
        """
        if satellite_id not in self.satellites:
            self.logger.error(f"Satellite {satellite_id} not found")
            return False
        
        satellite = self.satellites[satellite_id]
        success = satellite.add_task(task)
        
        if success:
            self.total_tasks_assigned += 1
            self.logger.info(f"Assigned task {task.task_id} to satellite {satellite_id}")
        else:
            self.logger.warning(f"Failed to assign task {task.task_id} to satellite {satellite_id}")
        
        return success
    
    @handle_errors(module="compute_manager", function="assign_task_to_cloud")
    def assign_task_to_cloud(self, task: Task, cloud_id: int, arrival_time: float = 0.0) -> bool:
        """
        Assign task to specific cloud center
        
        Args:
            task: Task to assign
            cloud_id: Target cloud ID
            arrival_time: Task arrival time
            
        Returns:
            True if successfully assigned, False otherwise
        """
        if cloud_id not in self.clouds:
            self.logger.error(f"Cloud {cloud_id} not found")
            return False
        
        cloud = self.clouds[cloud_id]
        success = cloud.add_task(task, arrival_time)
        
        if success:
            self.total_tasks_assigned += 1
            self.logger.info(f"Assigned task {task.task_id} to cloud {cloud_id}")
        else:
            self.logger.warning(f"Failed to assign task {task.task_id} to cloud {cloud_id}")
        
        return success
    
    @handle_errors(module="compute_manager", function="process_timeslot")
    def process_timeslot(self, timeslot: int, duration: float, 
                        illuminated_status: Dict[int, bool]) -> Dict[str, Any]:
        """
        Process all compute nodes for one timeslot
        
        Args:
            timeslot: Current timeslot number
            duration: Timeslot duration in seconds
            illuminated_status: Dictionary of satellite illumination status
            
        Returns:
            Processing results and metrics
        """
        results = {
            'satellite_results': {},
            'cloud_results': {},
            'total_completed': 0,
            'total_dropped': 0,
            'total_energy_consumed': 0.0,
            'system_metrics': {}
        }
        
        # Process satellites
        for sat_id, satellite in self.satellites.items():
            illuminated = illuminated_status.get(sat_id, True)
            sat_result = satellite.process_timeslot(duration, illuminated)
            results['satellite_results'][sat_id] = sat_result.to_dict()
            results['total_completed'] += len(sat_result.completed_tasks)
            results['total_energy_consumed'] += sat_result.energy_consumed
        
        # Process clouds
        for cloud_id, cloud in self.clouds.items():
            completed_tasks = cloud.process_batch(duration)
            cloud_result = {
                'completed_count': len(completed_tasks),
                'queue_length': cloud.get_queue_length(),
                'capacity_info': cloud.get_processing_capacity()
            }
            results['cloud_results'][cloud_id] = cloud_result
            results['total_completed'] += len(completed_tasks)
        
        # Update system metrics
        self._update_system_metrics()
        results['system_metrics'] = self.resource_metrics.to_dict()
        
        self.logger.debug(f"Timeslot {timeslot}: Completed {results['total_completed']} tasks, "
                         f"Energy consumed: {results['total_energy_consumed']:.2e}J")
        
        return results
    
    def _update_system_metrics(self):
        """Update system-wide resource metrics"""
        # Clear previous metrics
        self.resource_metrics.satellite_metrics.clear()
        self.resource_metrics.cloud_metrics.clear()
        
        # Update satellite metrics
        for sat_id, satellite in self.satellites.items():
            queue_status = satellite.get_queue_status()
            energy_status = satellite.get_energy_status()
            stats = satellite.get_statistics()
            
            sat_state = SatelliteState(
                node_id=sat_id,
                node_type=NodeType.SATELLITE,
                max_queue_size=satellite.scheduler.max_queue_size,
                current_queue_length=queue_status['queue_length'],
                is_available=energy_status['can_accept_tasks'],
                utilization_rate=queue_status['cpu_utilization'],
                battery_energy=energy_status['battery_energy'],
                max_battery=energy_status['max_battery'],
                illuminated=True,  # Will be updated from orbital data
                energy_consumption_rate=0.0,  # To be calculated
                cpu_frequency=satellite.cpu_frequency,
                available_cpu=1.0 if not queue_status['is_processing'] else 0.0,
                total_tasks_processed=stats['total_tasks_processed'],
                total_tasks_dropped=stats['total_tasks_dropped'],
                total_energy_consumed=stats['total_energy_consumed'],
                total_penalty=stats.get('total_penalty', 0.0)
            )
            self.resource_metrics.satellite_metrics[sat_id] = sat_state
        
        # Update cloud metrics
        for cloud_id, cloud in self.clouds.items():
            queue_status = cloud.get_queue_status()
            capacity_info = cloud.get_processing_capacity()
            stats = cloud.get_statistics()
            
            cloud_state = CloudState(
                node_id=cloud_id,
                node_type=NodeType.CLOUD,
                max_queue_size=cloud.max_queue_size,
                current_queue_length=queue_status['queue_length'],
                is_available=True,  # Clouds are always available
                utilization_rate=capacity_info['utilization_percentage'],
                cpu_frequency=cloud.cpu_frequency,
                max_parallel_tasks=cloud.max_parallel_tasks,
                current_parallel_count=queue_status['processing_count'],
                total_capacity=capacity_info['total_capacity'],
                available_capacity=capacity_info['available_capacity'],
                total_tasks_processed=stats['total_tasks_processed'],
                total_processing_time=stats['total_processing_time']
            )
            self.resource_metrics.cloud_metrics[cloud_id] = cloud_state
        
        # Calculate aggregate metrics
        self.resource_metrics.calculate_system_metrics()
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        return {
            'total_tasks_assigned': self.total_tasks_assigned,
            'num_satellites': len(self.satellites),
            'num_clouds': len(self.clouds),
            'resource_metrics': self.resource_metrics.to_dict()
        }
    
    def get_satellite_status(self, satellite_id: int) -> Optional[Dict[str, Any]]:
        """Get specific satellite status"""
        if satellite_id not in self.satellites:
            return None
        
        satellite = self.satellites[satellite_id]
        return {
            'queue_status': satellite.get_queue_status(),
            'energy_status': satellite.get_energy_status(),
            'statistics': satellite.get_statistics()
        }
    
    def get_cloud_status(self, cloud_id: int) -> Optional[Dict[str, Any]]:
        """Get specific cloud status"""
        if cloud_id not in self.clouds:
            return None
        
        cloud = self.clouds[cloud_id]
        return {
            'queue_status': cloud.get_queue_status(),
            'capacity_info': cloud.get_processing_capacity(),
            'statistics': cloud.get_statistics()
        }
    
    def get_all_satellite_status(self) -> Dict[int, Dict[str, Any]]:
        """Get status of all satellites"""
        status = {}
        for sat_id in self.satellites:
            status[sat_id] = self.get_satellite_status(sat_id)
        return status
    
    def get_all_cloud_status(self) -> Dict[int, Dict[str, Any]]:
        """Get status of all clouds"""
        status = {}
        for cloud_id in self.clouds:
            status[cloud_id] = self.get_cloud_status(cloud_id)
        return status
    
    def reset(self):
        """Reset all compute resources"""
        for satellite in self.satellites.values():
            satellite.reset()
        
        for cloud in self.clouds.values():
            cloud.reset()
        
        self.total_tasks_assigned = 0
        self.resource_metrics = ResourceMetrics()
        
        self.logger.info("Reset compute manager")
    
    def get_satellite(self, satellite_id: int) -> Optional[SatelliteCompute]:
        """Get satellite compute instance"""
        return self.satellites.get(satellite_id)
    
    def get_cloud(self, cloud_id: int) -> Optional[CloudCompute]:
        """Get cloud compute instance"""
        return self.clouds.get(cloud_id)
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get basic performance summary"""
        satellite_performance = {}
        for sat_id, satellite in self.satellites.items():
            satellite_performance[sat_id] = satellite.get_statistics()
        
        cloud_performance = {}
        for cloud_id, cloud in self.clouds.items():
            cloud_performance[cloud_id] = cloud.get_statistics()
        
        return {
            'satellite_performance': satellite_performance,
            'cloud_performance': cloud_performance,
            'total_tasks_assigned': self.total_tasks_assigned,
            'system_metrics': self.resource_metrics.to_dict()
        }