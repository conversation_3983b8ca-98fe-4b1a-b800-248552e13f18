/**
 * SPACE2 Details Panel
 * 处理卫星详细信息面板的显示和更新
 */

class DetailsPanel {
    constructor() {
        this.currentSatellite = null;
        this.currentTimeslot = 0;
        
        // 缓存DOM元素
        this.elements = {
            selectedSatellite: document.getElementById('selected-satellite'),
            satId: document.getElementById('sat-id'),
            satPosition: document.getElementById('sat-position'),
            satStatus: document.getElementById('sat-status'),
            satLight: document.getElementById('sat-light'),
            satAltitude: document.getElementById('sat-altitude'),
            satConnections: document.getElementById('sat-connections'),
            groundConnections: document.getElementById('ground-connections'),
            cloudConnections: document.getElementById('cloud-connections'),
            totalBandwidth: document.getElementById('total-bandwidth'),
            energyLevel: document.getElementById('energy-level'),
            cpuUsage: document.getElementById('cpu-usage'),
            memoryUsage: document.getElementById('memory-usage'),
            taskQueue: document.getElementById('task-queue')
        };\n        \n        // 初始化\n        this.showNoSelection();\n        \n        console.log('DetailsPanel initialized');\n    }\n    \n    showNoSelection() {\n        this.elements.selectedSatellite.textContent = '点击选择卫星';\n        this.elements.selectedSatellite.className = 'selected-satellite';\n        \n        // 重置所有信息显示\n        this.elements.satId.textContent = '-';\n        this.elements.satPosition.textContent = '-';\n        this.elements.satStatus.textContent = '-';\n        this.elements.satLight.textContent = '-';\n        this.elements.satConnections.textContent = '0';\n        this.elements.groundConnections.textContent = '0';\n        this.elements.cloudConnections.textContent = '0';\n        this.elements.totalBandwidth.textContent = '0 Gbps';\n        this.elements.energyLevel.textContent = '-';\n        this.elements.cpuUsage.textContent = '-';\n        this.elements.memoryUsage.textContent = '-';\n        \n        this.elements.taskQueue.innerHTML = `\n            <div class=\"no-selection\">\n                请选择卫星查看任务\n            </div>\n        `;\n        \n        this.currentSatellite = null;\n    }\n    \n    async updateSatellite(satelliteId, timeslot) {\n        this.currentSatellite = satelliteId;\n        this.currentTimeslot = timeslot;\n        \n        // 更新选中卫星显示\n        this.elements.selectedSatellite.textContent = satelliteId;\n        this.elements.selectedSatellite.className = 'selected-satellite';\n        \n        try {\n            // 显示加载状态\n            this.showLoading();\n            \n            // 获取卫星详细信息\n            const response = await fetch(`/api/satellite/${satelliteId}/details/${timeslot}`);\n            \n            if (!response.ok) {\n                throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n            }\n            \n            const data = await response.json();\n            \n            if (data.error) {\n                this.showError(data.error);\n                return;\n            }\n            \n            // 更新界面\n            this.renderBasicInfo(data.basic_info);\n            this.renderConnectionStats(data.connections);\n            this.renderPerformanceMetrics(data.performance);\n            this.renderTaskQueue(data.tasks);\n            \n            console.log(`Updated details for satellite ${satelliteId}`);\n            \n        } catch (error) {\n            console.error('Error updating satellite details:', error);\n            this.showError(error.message);\n        }\n    }\n    \n    showLoading() {\n        this.elements.taskQueue.innerHTML = `\n            <div class=\"no-selection\">\n                <div class=\"loading-spinner\" style=\"width: 20px; height: 20px; margin: 0 auto 10px;\"></div>\n                正在加载...\n            </div>\n        `;\n    }\n    \n    showError(errorMessage) {\n        this.elements.selectedSatellite.textContent = '加载错误';\n        this.elements.selectedSatellite.className = 'selected-satellite error';\n        \n        this.elements.taskQueue.innerHTML = `\n            <div class=\"no-selection\" style=\"color: #ff4444;\">\n                <strong>错误:</strong><br>\n                ${errorMessage}\n            </div>\n        `;\n    }\n    \n    renderBasicInfo(basicInfo) {\n        this.elements.satId.textContent = basicInfo.satellite_id;\n        this.elements.satPosition.textContent = \n            `(${basicInfo.latitude.toFixed(2)}°, ${basicInfo.longitude.toFixed(2)}°)`;\n        \n        // 状态显示\n        const statusText = basicInfo.status === 'damaged' ? '损坏' : '正常';\n        this.elements.satStatus.textContent = statusText;\n        this.elements.satStatus.className = `stat-value status-${basicInfo.status === 'damaged' ? 'damaged' : 'normal'}`;\n        \n        // 光照状态\n        const lightText = basicInfo.light_status || (basicInfo.illuminated ? 'Light' : 'Shadow');\n        this.elements.satLight.textContent = lightText;\n        this.elements.satLight.className = `stat-value ${basicInfo.illuminated ? 'status-normal' : 'status-shadow'}`;\n        \n        // 高度\n        this.elements.satAltitude.textContent = `${basicInfo.altitude_km} km`;\n    }\n    \n    renderConnectionStats(connections) {\n        this.elements.satConnections.textContent = connections.satellite_count;\n        this.elements.groundConnections.textContent = connections.ground_count;\n        this.elements.cloudConnections.textContent = connections.cloud_count;\n        this.elements.totalBandwidth.textContent = `${connections.total_bandwidth_gbps} Gbps`;\n        \n        // 如果卫星损坏，显示特殊样式\n        if (connections.status === 'damaged') {\n            [this.elements.satConnections, this.elements.groundConnections, \n             this.elements.cloudConnections, this.elements.totalBandwidth].forEach(el => {\n                el.className = 'stat-value status-damaged';\n            });\n        } else {\n            [this.elements.satConnections, this.elements.groundConnections, \n             this.elements.cloudConnections, this.elements.totalBandwidth].forEach(el => {\n                el.className = 'stat-value';\n            });\n        }\n    }\n    \n    renderPerformanceMetrics(performance) {\n        if (performance.status === 'offline') {\n            // 损坏卫星显示离线状态\n            this.elements.energyLevel.textContent = 'OFFLINE';\n            this.elements.cpuUsage.textContent = 'OFFLINE';\n            this.elements.memoryUsage.textContent = 'OFFLINE';\n            \n            [this.elements.energyLevel, this.elements.cpuUsage, this.elements.memoryUsage].forEach(el => {\n                el.className = 'stat-value status-damaged';\n            });\n        } else {\n            // 正常卫星显示性能指标\n            this.elements.energyLevel.textContent = `${performance.energy_level}%`;\n            this.elements.cpuUsage.textContent = `${performance.cpu_usage}%`;\n            this.elements.memoryUsage.textContent = `${performance.memory_usage}%`;\n            \n            // 根据指标值设置颜色\n            this.setPerformanceColor(this.elements.energyLevel, performance.energy_level, false); // 能耗低更好\n            this.setPerformanceColor(this.elements.cpuUsage, performance.cpu_usage, true); // CPU使用率\n            this.setPerformanceColor(this.elements.memoryUsage, performance.memory_usage, true); // 内存使用率\n        }\n    }\n    \n    setPerformanceColor(element, value, isUsage) {\n        let colorClass = 'status-normal';\n        \n        if (isUsage) {\n            // 使用率类指标\n            if (value > 80) colorClass = 'status-damaged';\n            else if (value > 60) colorClass = 'status-shadow';\n            else colorClass = 'status-normal';\n        } else {\n            // 能耗类指标（越低越好）\n            if (value < 30) colorClass = 'status-damaged';\n            else if (value < 50) colorClass = 'status-shadow';\n            else colorClass = 'status-normal';\n        }\n        \n        element.className = `stat-value ${colorClass}`;\n    }\n    \n    renderTaskQueue(tasks) {\n        if (!tasks || tasks.length === 0) {\n            this.elements.taskQueue.innerHTML = `\n                <div class=\"no-selection\">\n                    当前无任务\n                </div>\n            `;\n            return;\n        }\n        \n        const tasksHtml = tasks.map(task => this.createTaskItemHtml(task)).join('');\n        this.elements.taskQueue.innerHTML = tasksHtml;\n        \n        // 添加任务项动画\n        this.animateTaskItems();\n    }\n    \n    createTaskItemHtml(task) {\n        const statusMap = {\n            'processing': '处理中',\n            'queued': '排队中',\n            'completed': '已完成',\n            'failed': '失败'\n        };\n        \n        const priorityMap = {\n            'high': '高',\n            'medium': '中',\n            'low': '低'\n        };\n        \n        const statusText = statusMap[task.status] || task.status;\n        const priorityText = priorityMap[task.priority] || task.priority;\n        \n        return `\n            <div class=\"task-item fade-in\">\n                <div class=\"task-header\">\n                    <span class=\"task-id\">${task.task_id}</span>\n                    <span class=\"task-status ${task.status}\">${statusText}</span>\n                </div>\n                \n                <div class=\"task-progress\">\n                    <div class=\"progress-bar\" style=\"width: ${task.progress}%\"></div>\n                </div>\n                \n                <div class=\"task-info\">\n                    <span>${task.data_size_mb} MB</span>\n                    <span class=\"task-priority ${task.priority}\">${priorityText}</span>\n                </div>\n                \n                <div class=\"task-details\">\n                    <small>来源: ${task.source}</small>\n                    <small>截止: ${task.deadline}</small>\n                </div>\n            </div>\n        `;\n    }\n    \n    animateTaskItems() {\n        const taskItems = this.elements.taskQueue.querySelectorAll('.task-item');\n        taskItems.forEach((item, index) => {\n            item.style.animationDelay = `${index * 0.1}s`;\n        });\n    }\n    \n    // 更新任务进度条动画\n    updateTaskProgress(taskId, progress) {\n        const taskItem = this.elements.taskQueue.querySelector(`[data-task-id=\"${taskId}\"]`);\n        if (taskItem) {\n            const progressBar = taskItem.querySelector('.progress-bar');\n            if (progressBar) {\n                progressBar.style.width = `${progress}%`;\n            }\n        }\n    }\n    \n    // 高亮连接统计\n    highlightConnectionType(connectionType) {\n        // 移除所有高亮\n        [this.elements.satConnections, this.elements.groundConnections, \n         this.elements.cloudConnections].forEach(el => {\n            el.classList.remove('highlight');\n        });\n        \n        // 添加指定类型的高亮\n        switch(connectionType) {\n            case 'satellite':\n                this.elements.satConnections.classList.add('highlight');\n                break;\n            case 'ground':\n                this.elements.groundConnections.classList.add('highlight');\n                break;\n            case 'cloud':\n                this.elements.cloudConnections.classList.add('highlight');\n                break;\n        }\n    }\n    \n    // 获取当前选中的卫星\n    getCurrentSatellite() {\n        return this.currentSatellite;\n    }\n    \n    // 获取当前时隙\n    getCurrentTimeslot() {\n        return this.currentTimeslot;\n    }\n    \n    // 刷新当前显示的信息\n    async refresh() {\n        if (this.currentSatellite && this.currentTimeslot !== null) {\n            await this.updateSatellite(this.currentSatellite, this.currentTimeslot);\n        }\n    }\n    \n    // 清除选择\n    clearSelection() {\n        this.showNoSelection();\n    }\n}"