#!/usr/bin/env python3
"""
SPACE2 Visualization API Test Script
测试可视化API的基本功能
"""

import sys
import json
import traceback
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.env.visualization.data_adapter import VisualizationDataAdapter


def test_data_adapter():
    """测试数据适配器"""
    print("Testing VisualizationDataAdapter...")
    
    try:
        # 创建数据适配器
        adapter = VisualizationDataAdapter()
        print("+ DataAdapter created successfully")
        
        # 测试全局状态
        print("\nTesting global state (timeslot 0)...")
        global_state = adapter.get_global_state(0)
        print(f"+ Satellites: {len(global_state['satellites'])}")
        print(f"+ Ground stations: {len(global_state['ground_stations'])}")
        print(f"+ Cloud centers: {len(global_state['cloud_centers'])}")
        print(f"+ Active satellites: {global_state['active_satellites']}")
        print(f"+ Damaged satellites: {global_state['damaged_satellites']}")
        
        # 测试卫星详情
        if global_state['satellites']:
            satellite_id = global_state['satellites'][0]['id']
            print(f"\nTesting satellite details ({satellite_id})...")
            details = adapter.get_satellite_details(satellite_id, 0)
            
            if 'error' not in details:
                print(f"+ Basic info: {details['basic_info']['status']}")
                print(f"+ Connections: {details['connections']['satellite_count']} sat, "
                      f"{details['connections']['ground_count']} ground, "
                      f"{details['connections']['cloud_count']} cloud")
                print(f"+ Tasks: {len(details['tasks'])}")
            else:
                print(f"⚠ Error getting details: {details['error']}")
        
        # 测试连接信息
        if global_state['satellites'] and global_state['active_satellites'] > 0:
            # 找一个正常的卫星
            active_satellite = None
            for sat in global_state['satellites']:
                if sat['state']:
                    active_satellite = sat['id']
                    break
            
            if active_satellite:
                print(f"\nTesting satellite connections ({active_satellite})...")
                connections = adapter.get_satellite_connections(active_satellite, 0)
                
                if 'error' not in connections:
                    print(f"+ Satellite connections: {len(connections['satellite_connections'])}")
                    print(f"+ Ground connections: {len(connections['ground_connections'])}")
                    print(f"+ Cloud connections: {len(connections['cloud_connections'])}")
                else:
                    print(f"⚠ Error getting connections: {connections['error']}")
        
        # 测试系统统计
        print(f"\nTesting system statistics...")
        stats = adapter.get_system_statistics(0)
        print(f"+ System health: {stats['system_health']:.2%}")
        
        print("\n[SUCCESS] All tests passed!")
        return True
        
    except Exception as e:
        print(f"\n[ERROR] Test failed: {e}")
        print(traceback.format_exc())
        return False


def test_different_timeslots():
    """测试不同时隙的数据"""
    print("\n" + "="*50)
    print("Testing different timeslots...")
    
    try:
        adapter = VisualizationDataAdapter()
        
        test_timeslots = [0, 100, 500, 1000, 1440]
        
        for timeslot in test_timeslots:
            try:
                print(f"\nTimeslot {timeslot}:")
                global_state = adapter.get_global_state(timeslot)
                print(f"  Satellites: {global_state['total_satellites']}")
                print(f"  Active: {global_state['active_satellites']}")
                print(f"  Damaged: {global_state['damaged_satellites']}")
                
                # 检查是否有损坏的卫星
                damaged_satellites = [s for s in global_state['satellites'] if not s['state']]
                if damaged_satellites:
                    print(f"  Damaged satellite IDs: {[s['id'] for s in damaged_satellites[:5]]}")
                
            except Exception as e:
                print(f"  ⚠ Error at timeslot {timeslot}: {e}")
        
        print("\n[SUCCESS] Timeslot tests completed!")
        return True
        
    except Exception as e:
        print(f"\n[ERROR] Timeslot test failed: {e}")
        return False


def main():
    """主测试函数"""
    print("SPACE2 Visualization API Test")
    print("="*50)
    
    # 基本功能测试
    success1 = test_data_adapter()
    
    # 不同时隙测试
    success2 = test_different_timeslots()
    
    print("\n" + "="*50)
    if success1 and success2:
        print("[SUCCESS] All tests passed! The visualization system is ready.")
        print("\nNext steps:")
        print("1. Install Flask dependencies: pip install -r requirements.txt")
        print("2. Start the server: python run_visualization.py")
        print("3. Open browser: http://localhost:5000")
    else:
        print("[ERROR] Some tests failed. Please check the errors above.")
        print("\nTroubleshooting:")
        print("1. Check if all data files exist in src/env/env_data/")
        print("2. Verify orbital.py and communication_refactored.py work correctly")
        print("3. Check config.yaml settings")


if __name__ == '__main__':
    main()