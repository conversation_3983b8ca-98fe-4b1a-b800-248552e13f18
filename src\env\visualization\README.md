# SPACE2 卫星星座可视化系统

SPACE2卫星星座仿真的Web可视化界面，提供3D地球视图和实时数据展示。

## 功能特性

### 🌍 全局3D视图
- **3D地球模型**：基于Three.js的高质量3D地球渲染
- **72颗LEO卫星**：实时显示所有卫星位置和状态
- **420个地面站**：全球分布的用户终端显示
- **5个云中心**：云计算节点位置标识

### 🛰️ 卫星状态可视化
- **正常卫星**：绿色显示，支持光照/阴影状态区分
- **损坏卫星**：红色显示，不显示连接关系
- **状态指标**：ID标签、位置信息、工作状态

### 🔗 连接关系可视化
- **卫星间连接**：青色实线，激光通信链路
- **地面站连接**：黄色虚线，RF上下行链路  
- **云中心连接**：橙色粗线，高带宽连接
- **动态线宽**：根据数据传输速率调整线条粗细

### 📊 详细信息面板
- **基本信息**：位置、状态、光照、高度
- **连接统计**：各类连接数量和总带宽
- **性能指标**：能耗、CPU使用率、内存使用率
- **任务队列**：当前处理和排队任务列表

### ⚡ 交互控制
- **时隙跳转**：输入任意时隙(0-1441)查看状态
- **卫星选择**：点击卫星查看详细信息和连接
- **视角控制**：鼠标拖拽、缩放、自动旋转
- **键盘快捷键**：方向键切换时隙，空格控制旋转

## 技术架构

### 后端 (Python)
```
src/env/visualization/
├── api_server.py          # Flask API服务器
├── data_adapter.py        # 数据适配器
├── run_visualization.py   # 启动脚本
└── static/               # 前端静态文件
```

### 前端 (JavaScript)
```
static/
├── index.html            # 主页面
├── css/style.css         # 样式文件
└── js/
    ├── main.js           # 主应用程序
    ├── earth-view.js     # 3D地球视图
    └── details-panel.js  # 详细信息面板
```

### 依赖库
- **后端**: Flask, NumPy, Pandas, PyYAML
- **前端**: Three.js, OrbitControls
- **集成**: orbital.py, communication_refactored.py

## 安装和使用

### 1. 环境要求
- Python 3.9+
- 现代Web浏览器 (Chrome, Firefox, Edge)

### 2. 安装依赖
```bash
pip install flask flask-cors numpy pandas pyyaml
```

### 3. 检查系统
```bash
cd src/env/visualization
python run_visualization.py --check-only
```

### 4. 启动服务器
```bash
# 默认启动 (localhost:5000)
python run_visualization.py

# 自定义端口
python run_visualization.py --port 8080

# 允许外部访问
python run_visualization.py --host 0.0.0.0

# 调试模式
python run_visualization.py --debug
```

### 5. 打开浏览器
访问 `http://localhost:5000` (或指定的地址)

## 使用指南

### 基本操作
1. **加载时隙**: 在时隙输入框输入0-1441的数字，点击"加载"
2. **选择卫星**: 点击3D视图中的任意卫星
3. **查看连接**: 选中卫星后自动显示其所有连接关系
4. **控制视角**: 
   - 鼠标左键拖拽：旋转视角
   - 鼠标滚轮：缩放
   - 鼠标右键拖拽：平移

### 键盘快捷键
- `←/→`: 切换时隙
- `Home/End`: 跳转到首/末时隙
- `Space`: 暂停/继续自动旋转
- `R`: 重置视角
- `C`: 清除连接线
- `Ctrl+F`: 聚焦时隙输入框

### 卫星状态说明
- 🟢 **绿色卫星**: 正常工作，处于光照状态
- ⚫ **灰色卫星**: 正常工作，处于阴影状态  
- 🔴 **红色卫星**: 损坏状态，无连接关系

### 连接线说明
- **青色实线**: 卫星间激光通信（ISL）
- **黄色虚线**: 卫星-地面站RF连接
- **橙色粗线**: 卫星-云中心高速连接

## API接口

### 全局状态
```
GET /api/timeslot/{timeslot}/global_state
```
返回指定时隙的所有卫星、地面站、云中心位置

### 卫星连接
```
GET /api/satellite/{satellite_id}/connections/{timeslot}
```
返回指定卫星的所有连接关系

### 卫星详情
```
GET /api/satellite/{satellite_id}/details/{timeslot}
```
返回卫星的详细信息、任务、性能指标

### 系统统计
```
GET /api/system/statistics/{timeslot}
```
返回系统整体统计信息

## 故障排除

### 常见问题

**1. 服务器启动失败**
- 检查端口是否被占用
- 确认依赖包已安装
- 使用 `--debug` 查看详细错误

**2. 数据加载失败**
- 确认数据文件存在：
  - `src/env/env_data/satellite_data72_1.csv`
  - `src/env/env_data/global_ground_stations.csv`
  - `src/env/env_data/cloud_station.csv`

**3. 3D视图不显示**
- 检查浏览器是否支持WebGL
- 尝试刷新页面
- 检查浏览器控制台错误信息

**4. 性能问题**
- 减少同时显示的连接线数量
- 使用现代浏览器和显卡
- 调整窗口大小以适配设备性能

### 调试模式
```bash
python run_visualization.py --debug
```
启用调试模式可以：
- 查看详细的错误信息
- 实时重载代码修改
- 输出详细的API日志

### 浏览器调试
打开浏览器开发者工具 (F12)：
- **Console**: 查看JavaScript错误和日志
- **Network**: 检查API请求状态
- **Performance**: 监控渲染性能

## 开发指南

### 代码结构
系统采用前后端分离架构：
- 后端提供RESTful API接口
- 前端使用Three.js渲染3D场景
- 数据适配器负责格式转换

### 扩展功能
1. **添加新的可视化元素**：修改 `earth-view.js`
2. **扩展API接口**：修改 `api_server.py`
3. **增加数据处理**：修改 `data_adapter.py`
4. **调整界面样式**：修改 `style.css`

### 性能优化
- 使用数据缓存减少重复计算
- 按需加载连接关系
- 优化3D模型复杂度
- 合理设置渲染频率

## 版本信息

- **版本**: 1.0.0
- **更新日期**: 2024-08-16
- **兼容性**: SPACE2 v2.0+
- **许可证**: SPACE2 Project

## 联系方式

如有问题或建议，请联系SPACE2开发团队。