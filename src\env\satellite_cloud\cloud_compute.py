"""
Cloud compute resource management module
Implements high-performance cloud computing simulation
No energy constraints, high CPU frequency, parallel processing support
"""

import numpy as np
from typing import List, Dict, Optional, Any
import logging
import yaml
from pathlib import Path

# Absolute imports as per coding standard
from src.env.satellite_cloud.compute_models import (
    ComputeTask, TaskStatus, CloudState, ProcessingResult
)
from src.env.physics_layer.task_models import Task
from src.env.Foundation_Layer.error_handling import handle_errors, SpaceSimulationError
from src.env.Foundation_Layer.logging_config import get_logger


class CloudCompute:
    """Cloud computing resource manager"""
    
    def __init__(self, cloud_id: int, config_path: str = None, 
                 config: Dict = None):
        """
        Initialize cloud compute manager
        
        Args:
            cloud_id: Cloud center ID
            config_path: Path to configuration file
            config: Configuration dictionary (if config_path not provided)
        """
        self.cloud_id = cloud_id
        
        # Load configuration - must be provided as per coding standard
        if config is not None:
            self.config = config
        elif config_path is not None:
            with open(config_path, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)
        else:
            raise ValueError("Either config_path or config must be provided")
        
        # Extract configuration parameters - no default values as per coding standard
        computation_config = self.config['computation']
        self.cpu_frequency = float(computation_config['f_cloud_hz'])  # Much higher than satellite
        self.max_parallel_tasks = computation_config['max_parallel_tasks']
        
        # Queue management (cloud has much larger capacity)
        queuing_config = self.config['queuing']
        cloud_multiplier = queuing_config['cloud_queue_multiplier']
        self.max_queue_size = queuing_config['max_queue_size'] * cloud_multiplier
        
        # Processing state
        self.task_queue: List[ComputeTask] = []
        self.processing_tasks: List[ComputeTask] = []
        self.completed_tasks: List[ComputeTask] = []
        
        # Resource tracking
        self.total_capacity = self.cpu_frequency * self.max_parallel_tasks
        self.available_capacity = self.total_capacity
        
        # Performance metrics
        self.total_tasks_processed = 0
        self.total_processing_time = 0.0
        self.total_queue_wait_time = 0.0
        
        # Scheduling policy (FIFO for cloud)
        self.scheduling_policy = "FIFO"  # Can be extended to other policies
        
        self.logger = get_logger(f"CloudCompute_{cloud_id}")
        
    @handle_errors(module="cloud_compute", function="add_task")
    def add_task(self, task: Task, arrival_time: float = 0.0) -> bool:
        """
        Add task to cloud processing queue
        
        Args:
            task: Task object from task_models
            arrival_time: Task arrival time
            
        Returns:
            True if successfully added, False if rejected
        """
        # Check queue capacity (cloud has very large capacity)
        if len(self.task_queue) >= self.max_queue_size:
            self.logger.warning(f"Cloud queue full, rejecting task {task.task_id}")
            return False
        
        # Convert Task to ComputeTask
        compute_task = ComputeTask.from_task(
            task, 
            arrival_time=arrival_time,
            drop_penalty=0.0  # Cloud doesn't drop tasks due to energy
        )
        
        # Cloud has no energy constraints - always accept if queue has space
        self.task_queue.append(compute_task)
        self.logger.info(f"Added task {task.task_id} to cloud queue (queue size: {len(self.task_queue)})")
        return True
    
    @handle_errors(module="cloud_compute", function="process_batch")
    def process_batch(self, duration: float) -> List[ComputeTask]:
        """
        Process tasks in parallel for given duration
        
        Args:
            duration: Processing duration in seconds
            
        Returns:
            List of completed tasks
        """
        completed_tasks = []
        
        # Move tasks from queue to processing if there's capacity
        while (len(self.processing_tasks) < self.max_parallel_tasks and 
               len(self.task_queue) > 0):
            task = self._select_next_task()
            if task:
                task.status = TaskStatus.PROCESSING
                task.start_time = 0.0  # Will be set by caller
                self.processing_tasks.append(task)
                self.logger.debug(f"Started processing task {task.task_id}")
        
        # Process all currently processing tasks in parallel
        remaining_tasks = []
        for task in self.processing_tasks:
            completed_task = self._process_task(task, duration)
            if completed_task:
                completed_tasks.append(completed_task)
                self.completed_tasks.append(completed_task)
                self.total_tasks_processed += 1
            else:
                remaining_tasks.append(task)
        
        self.processing_tasks = remaining_tasks
        
        # Update capacity
        self._update_capacity()
        
        self.logger.info(f"Processed {len(completed_tasks)} tasks, "
                        f"{len(self.processing_tasks)} still processing, "
                        f"{len(self.task_queue)} in queue")
        
        return completed_tasks
    
    def _select_next_task(self) -> Optional[ComputeTask]:
        """
        Select next task based on scheduling policy
        
        Returns:
            Selected task or None if no suitable task
        """
        if not self.task_queue:
            return None
        
        if self.scheduling_policy == "FIFO":
            # First In First Out
            return self.task_queue.pop(0)
        elif self.scheduling_policy == "PRIORITY":
            # Highest priority first
            self.task_queue.sort(key=lambda t: t.priority, reverse=True)
            return self.task_queue.pop(0)
        elif self.scheduling_policy == "SJF":
            # Shortest Job First
            self.task_queue.sort(key=lambda t: t.complexity)
            return self.task_queue.pop(0)
        else:
            # Default to FIFO
            return self.task_queue.pop(0)
    
    def _process_task(self, task: ComputeTask, duration: float) -> Optional[ComputeTask]:
        """
        Process individual task for given duration
        
        Args:
            task: Task to process
            duration: Processing duration
            
        Returns:
            Completed task if finished, None otherwise
        """
        # Calculate processing capability (much higher than satellite)
        cycles_per_second = self.cpu_frequency
        cycles_available = cycles_per_second * duration
        
        # Cloud can allocate full CPU to each task (simplified model)
        cycles_needed = task.remaining_complexity
        cycles_processed = min(cycles_available, cycles_needed)
        
        # Update task progress
        task.remaining_complexity -= cycles_processed
        task.accumulated_processing_time += duration
        
        # Calculate progress
        if task.complexity > 0:
            task.processing_progress = 1.0 - (task.remaining_complexity / task.complexity)
        else:
            task.processing_progress = 1.0
        
        # Check if completed
        if task.remaining_complexity <= 0:
            task.status = TaskStatus.COMPLETED
            task.processing_progress = 1.0
            task.completion_time = 0.0  # Will be set by caller
            
            # Calculate processing delay (no communication delay for cloud internal processing)
            task.processing_delay = task.accumulated_processing_time
            task.communication_delay = 0.0  # Minimal internal communication
            task.total_delay = task.processing_delay
            
            # No energy consumption for cloud (unlimited power)
            task.energy_consumed = 0.0
            
            self.total_processing_time += task.accumulated_processing_time
            
            return task
        
        return None
    
    def _update_capacity(self):
        """Update available processing capacity"""
        used_capacity = len(self.processing_tasks) * self.cpu_frequency
        self.available_capacity = self.total_capacity - used_capacity
    
    def get_processing_capacity(self) -> Dict[str, Any]:
        """Get current processing capacity information"""
        utilization = (len(self.processing_tasks) / self.max_parallel_tasks) * 100
        
        return {
            'total_capacity': self.total_capacity,
            'available_capacity': self.available_capacity,
            'utilization_percentage': utilization,
            'max_parallel_tasks': self.max_parallel_tasks,
            'current_parallel_count': len(self.processing_tasks),
            'cpu_frequency': self.cpu_frequency
        }
    
    def get_queue_length(self) -> int:
        """Get current queue length"""
        return len(self.task_queue)
    
    def get_queue_status(self) -> Dict[str, Any]:
        """Get detailed queue status"""
        return {
            'queue_length': len(self.task_queue),
            'processing_count': len(self.processing_tasks),
            'completed_count': len(self.completed_tasks),
            'max_queue_size': self.max_queue_size,
            'queue_utilization': (len(self.task_queue) / self.max_queue_size) * 100,
            'processing_utilization': (len(self.processing_tasks) / self.max_parallel_tasks) * 100
        }
    
    def can_accept_task(self, task: ComputeTask) -> bool:
        """
        Check if cloud can accept task
        Cloud has no energy constraints, only queue capacity
        
        Args:
            task: Task to check
            
        Returns:
            True if can accept
        """
        return len(self.task_queue) < self.max_queue_size
    
    def estimate_completion_time(self, task: ComputeTask) -> float:
        """
        Estimate when task will be completed if added now
        
        Args:
            task: Task to estimate
            
        Returns:
            Estimated completion time in seconds
        """
        # Calculate queue waiting time
        queue_position = len(self.task_queue)
        processing_slots_available = max(0, self.max_parallel_tasks - len(self.processing_tasks))
        
        if queue_position < processing_slots_available:
            # Can start immediately
            queue_wait = 0.0
        else:
            # Need to wait for queue
            # Estimate based on average processing time of current tasks
            if self.processing_tasks:
                avg_remaining_time = sum(
                    t.remaining_complexity / self.cpu_frequency 
                    for t in self.processing_tasks
                ) / len(self.processing_tasks)
            else:
                avg_remaining_time = task.complexity / self.cpu_frequency
            
            queue_wait = avg_remaining_time * (queue_position - processing_slots_available) / self.max_parallel_tasks
        
        # Calculate processing time
        processing_time = task.complexity / self.cpu_frequency
        
        return queue_wait + processing_time
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get comprehensive statistics"""
        avg_processing_time = (self.total_processing_time / self.total_tasks_processed 
                              if self.total_tasks_processed > 0 else 0.0)
        
        return {
            'cloud_id': self.cloud_id,
            'total_tasks_processed': self.total_tasks_processed,
            'total_processing_time': self.total_processing_time,
            'average_processing_time': avg_processing_time,
            'queue_length': len(self.task_queue),
            'processing_count': len(self.processing_tasks),
            'completed_count': len(self.completed_tasks),
            'cpu_frequency': self.cpu_frequency,
            'max_parallel_tasks': self.max_parallel_tasks,
            'utilization': (len(self.processing_tasks) / self.max_parallel_tasks) * 100,
            'capacity_info': self.get_processing_capacity()
        }
    
    def set_scheduling_policy(self, policy: str):
        """
        Set scheduling policy
        
        Args:
            policy: "FIFO", "PRIORITY", or "SJF"
        """
        valid_policies = ["FIFO", "PRIORITY", "SJF"]
        if policy in valid_policies:
            self.scheduling_policy = policy
            self.logger.info(f"Set scheduling policy to {policy}")
        else:
            self.logger.warning(f"Invalid scheduling policy: {policy}. Using FIFO.")
            self.scheduling_policy = "FIFO"
    
    def reset(self):
        """Reset cloud state"""
        self.task_queue.clear()
        self.processing_tasks.clear()
        self.completed_tasks.clear()
        
        self.available_capacity = self.total_capacity
        self.total_tasks_processed = 0
        self.total_processing_time = 0.0
        self.total_queue_wait_time = 0.0
        
        self.logger.info(f"Reset cloud {self.cloud_id}")
    
    def get_performance_metrics(self) -> Dict[str, float]:
        """Get performance metrics for optimization"""
        throughput = (self.total_tasks_processed / self.total_processing_time 
                     if self.total_processing_time > 0 else 0.0)
        
        utilization = (len(self.processing_tasks) / self.max_parallel_tasks) * 100
        
        return {
            'throughput': throughput,
            'utilization': utilization,
            'queue_length': len(self.task_queue),
            'average_processing_time': (self.total_processing_time / self.total_tasks_processed 
                                      if self.total_tasks_processed > 0 else 0.0)
        }