/**
 * SPACE2 Visualization Main Application
 * 主应用程序，协调各个模块
 */

class SPACE2Visualization {
    constructor() {
        this.earthView = null;
        this.detailsPanel = null;
        this.currentTimeslot = 0;
        this.maxTimeslot = 1441;
        
        // DOM元素
        this.elements = {\n            timeslotInput: document.getElementById('timeslot-input'),\n            timeslotRange: document.getElementById('timeslot-range'),\n            loadButton: document.getElementById('load-timeslot'),\n            activeCount: document.getElementById('active-count'),\n            damagedCount: document.getElementById('damaged-count'),\n            loading: document.getElementById('loading'),\n            errorMessage: document.getElementById('error-message'),\n            errorText: document.getElementById('error-text'),\n            retryButton: document.getElementById('retry-button'),\n            resetViewButton: document.getElementById('reset-view'),\n            toggleRotationButton: document.getElementById('toggle-rotation'),\n            clearConnectionsButton: document.getElementById('clear-connections')\n        };\n        \n        this.init();\n    }\n    \n    async init() {\n        try {\n            // 初始化组件\n            this.earthView = new EarthView('earth-view');\n            this.detailsPanel = new DetailsPanel();\n            \n            // 设置事件监听\n            this.setupEventListeners();\n            \n            // 获取时隙范围\n            await this.loadTimeslotRange();\n            \n            // 加载初始数据\n            await this.loadTimeslot(0);\n            \n            console.log('SPACE2 Visualization initialized successfully');\n            \n        } catch (error) {\n            console.error('Failed to initialize application:', error);\n            this.showError('初始化失败: ' + error.message);\n        }\n    }\n    \n    setupEventListeners() {\n        // 时隙控制\n        this.elements.loadButton.addEventListener('click', () => {\n            const timeslot = parseInt(this.elements.timeslotInput.value);\n            this.loadTimeslot(timeslot);\n        });\n        \n        this.elements.timeslotInput.addEventListener('keypress', (event) => {\n            if (event.key === 'Enter') {\n                const timeslot = parseInt(this.elements.timeslotInput.value);\n                this.loadTimeslot(timeslot);\n            }\n        });\n        \n        // 视图控制按钮\n        if (this.elements.resetViewButton) {\n            this.elements.resetViewButton.addEventListener('click', () => {\n                this.earthView.resetView();\n            });\n        }\n        \n        if (this.elements.toggleRotationButton) {\n            this.elements.toggleRotationButton.addEventListener('click', () => {\n                this.earthView.toggleRotation();\n                this.elements.toggleRotationButton.textContent = \n                    this.earthView.isRotating ? '暂停旋转' : '继续旋转';\n            });\n        }\n        \n        if (this.elements.clearConnectionsButton) {\n            this.elements.clearConnectionsButton.addEventListener('click', () => {\n                this.earthView.clearConnections();\n                this.detailsPanel.clearSelection();\n            });\n        }\n        \n        // 错误重试\n        if (this.elements.retryButton) {\n            this.elements.retryButton.addEventListener('click', () => {\n                this.loadTimeslot(this.currentTimeslot);\n            });\n        }\n        \n        // 卫星点击事件\n        this.earthView.onSatelliteClick = async (satelliteId, satelliteData) => {\n            await this.onSatelliteSelected(satelliteId, satelliteData);\n        };\n        \n        // 卫星悬停事件\n        this.earthView.onSatelliteHover = (satelliteId, satelliteData) => {\n            this.showTooltip(satelliteId, satelliteData);\n        };\n        \n        // 键盘快捷键\n        window.addEventListener('keydown', (event) => {\n            this.handleKeyboardShortcuts(event);\n        });\n    }\n    \n    async loadTimeslotRange() {\n        try {\n            const response = await fetch('/api/timeslots/range');\n            if (response.ok) {\n                const data = await response.json();\n                this.maxTimeslot = data.max_timeslot;\n                this.elements.timeslotRange.textContent = `/ ${this.maxTimeslot + 1}`;\n                this.elements.timeslotInput.max = this.maxTimeslot;\n            }\n        } catch (error) {\n            console.warn('Failed to load timeslot range:', error);\n        }\n    }\n    \n    async loadTimeslot(timeslot) {\n        // 验证时隙范围\n        if (timeslot < 0 || timeslot > this.maxTimeslot) {\n            this.showError(`时隙超出范围 (0-${this.maxTimeslot})`);\n            return;\n        }\n        \n        this.currentTimeslot = timeslot;\n        this.elements.timeslotInput.value = timeslot;\n        \n        try {\n            // 显示加载状态\n            this.showLoading(true);\n            this.hideError();\n            \n            // 获取全局状态数据\n            const response = await fetch(`/api/timeslot/${timeslot}/global_state`);\n            \n            if (!response.ok) {\n                throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n            }\n            \n            const data = await response.json();\n            \n            if (data.error) {\n                throw new Error(data.error);\n            }\n            \n            // 更新3D视图\n            this.earthView.loadTimeslot(timeslot);\n            this.earthView.updateSatellites(data.satellites);\n            this.earthView.updateGroundStations(data.ground_stations);\n            this.earthView.updateCloudCenters(data.cloud_centers);\n            \n            // 更新统计信息\n            this.updateSystemStats(data);\n            \n            // 清除选择\n            this.detailsPanel.clearSelection();\n            \n            console.log(`Loaded timeslot ${timeslot}:`, data);\n            \n        } catch (error) {\n            console.error('Error loading timeslot:', error);\n            this.showError('加载时隙数据失败: ' + error.message);\n        } finally {\n            this.showLoading(false);\n        }\n    }\n    \n    async onSatelliteSelected(satelliteId, satelliteData) {\n        try {\n            console.log(`Satellite selected: ${satelliteId}`, satelliteData);\n            \n            // 检查卫星状态\n            if (!satelliteData.state) {\n                // 损坏卫星的处理\n                this.detailsPanel.updateSatellite(satelliteId, this.currentTimeslot);\n                this.showTooltip(satelliteId, satelliteData, '此卫星已损坏，无连接信息');\n                return;\n            }\n            \n            // 更新详细信息面板\n            await this.detailsPanel.updateSatellite(satelliteId, this.currentTimeslot);\n            \n            // 获取并显示连接信息\n            await this.loadSatelliteConnections(satelliteId);\n            \n        } catch (error) {\n            console.error('Error handling satellite selection:', error);\n            this.showError('获取卫星信息失败: ' + error.message);\n        }\n    }\n    \n    async loadSatelliteConnections(satelliteId) {\n        try {\n            const response = await fetch(`/api/satellite/${satelliteId}/connections/${this.currentTimeslot}`);\n            \n            if (!response.ok) {\n                throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n            }\n            \n            const connectionData = await response.json();\n            \n            if (connectionData.error) {\n                throw new Error(connectionData.error);\n            }\n            \n            // 在3D视图中显示连接\n            this.earthView.showSatelliteConnections(satelliteId, connectionData);\n            \n            console.log(`Loaded connections for ${satelliteId}:`, connectionData);\n            \n        } catch (error) {\n            console.error('Error loading satellite connections:', error);\n            // 连接加载失败不阻止卫星信息显示\n        }\n    }\n    \n    updateSystemStats(data) {\n        this.elements.activeCount.textContent = data.active_satellites || '-';\n        this.elements.damagedCount.textContent = data.damaged_satellites || '-';\n        \n        // 根据系统健康状况调整样式\n        const healthRatio = data.active_satellites / data.total_satellites;\n        \n        if (healthRatio < 0.7) {\n            this.elements.activeCount.className = 'status-damaged';\n        } else if (healthRatio < 0.9) {\n            this.elements.activeCount.className = 'status-shadow';\n        } else {\n            this.elements.activeCount.className = 'status-normal';\n        }\n    }\n    \n    showLoading(show) {\n        this.elements.loading.style.display = show ? 'block' : 'none';\n    }\n    \n    showError(message) {\n        this.elements.errorText.textContent = message;\n        this.elements.errorMessage.style.display = 'block';\n        this.showLoading(false);\n    }\n    \n    hideError() {\n        this.elements.errorMessage.style.display = 'none';\n    }\n    \n    showTooltip(satelliteId, satelliteData, customMessage = null) {\n        // 创建或更新工具提示\n        let tooltip = document.getElementById('satellite-tooltip');\n        if (!tooltip) {\n            tooltip = document.createElement('div');\n            tooltip.id = 'satellite-tooltip';\n            tooltip.className = 'tooltip';\n            document.body.appendChild(tooltip);\n        }\n        \n        const message = customMessage || this.getSatelliteTooltipText(satelliteData);\n        tooltip.innerHTML = message;\n        \n        // 显示工具提示\n        tooltip.style.display = 'block';\n        \n        // 跟随鼠标\n        document.addEventListener('mousemove', this.updateTooltipPosition);\n        \n        // 3秒后自动隐藏\n        setTimeout(() => {\n            if (tooltip) {\n                tooltip.style.display = 'none';\n                document.removeEventListener('mousemove', this.updateTooltipPosition);\n            }\n        }, 3000);\n    }\n    \n    getSatelliteTooltipText(satelliteData) {\n        const statusText = satelliteData.state ? '正常' : '损坏';\n        const lightText = satelliteData.illuminated ? '光照' : '阴影';\n        \n        return `\n            <strong>${satelliteData.id}</strong><br>\n            状态: ${statusText}<br>\n            光照: ${lightText}<br>\n            位置: (${satelliteData.latitude.toFixed(2)}°, ${satelliteData.longitude.toFixed(2)}°)\n        `;\n    }\n    \n    updateTooltipPosition(event) {\n        const tooltip = document.getElementById('satellite-tooltip');\n        if (tooltip) {\n            tooltip.style.left = (event.pageX + 10) + 'px';\n            tooltip.style.top = (event.pageY - 10) + 'px';\n        }\n    }\n    \n    handleKeyboardShortcuts(event) {\n        // 避免在输入框中触发快捷键\n        if (event.target.tagName === 'INPUT') {\n            return;\n        }\n        \n        switch(event.code) {\n            case 'ArrowLeft':\n                event.preventDefault();\n                this.loadTimeslot(Math.max(0, this.currentTimeslot - 1));\n                break;\n            case 'ArrowRight':\n                event.preventDefault();\n                this.loadTimeslot(Math.min(this.maxTimeslot, this.currentTimeslot + 1));\n                break;\n            case 'Home':\n                event.preventDefault();\n                this.loadTimeslot(0);\n                break;\n            case 'End':\n                event.preventDefault();\n                this.loadTimeslot(this.maxTimeslot);\n                break;\n            case 'KeyF':\n                if (event.ctrlKey) {\n                    event.preventDefault();\n                    this.focusTimeslotInput();\n                }\n                break;\n        }\n    }\n    \n    focusTimeslotInput() {\n        this.elements.timeslotInput.focus();\n        this.elements.timeslotInput.select();\n    }\n    \n    // 性能监控\n    startPerformanceMonitoring() {\n        setInterval(() => {\n            const stats = {\n                satellites: this.earthView.satellites.size,\n                connections: this.earthView.connectionLines.size,\n                memory: performance.memory ? performance.memory.usedJSHeapSize : 'N/A'\n            };\n            \n            console.log('Performance stats:', stats);\n        }, 30000); // 每30秒记录一次\n    }\n    \n    // API健康检查\n    async checkAPIHealth() {\n        try {\n            const response = await fetch('/api/health');\n            const data = await response.json();\n            console.log('API Health:', data);\n            return response.ok;\n        } catch (error) {\n            console.error('API health check failed:', error);\n            return false;\n        }\n    }\n}\n\n// 应用程序入口点\nwindow.addEventListener('DOMContentLoaded', () => {\n    console.log('SPACE2 Visualization starting...');\n    \n    try {\n        // 创建应用程序实例\n        window.space2App = new SPACE2Visualization();\n        \n        // 开始性能监控\n        window.space2App.startPerformanceMonitoring();\n        \n        // API健康检查\n        window.space2App.checkAPIHealth();\n        \n    } catch (error) {\n        console.error('Failed to start application:', error);\n        \n        // 显示启动错误\n        const errorDiv = document.createElement('div');\n        errorDiv.innerHTML = `\n            <div style=\"position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);\n                        background: #ff4444; color: white; padding: 20px; border-radius: 10px;\n                        text-align: center; z-index: 10000;\">\n                <h3>应用程序启动失败</h3>\n                <p>${error.message}</p>\n                <button onclick=\"location.reload()\" style=\"margin-top: 10px; padding: 5px 15px;\n                        background: white; color: #ff4444; border: none; border-radius: 5px; cursor: pointer;\">\n                    重新加载\n                </button>\n            </div>\n        `;\n        document.body.appendChild(errorDiv);\n    }\n});\n\n// 全局错误处理\nwindow.addEventListener('error', (event) => {\n    console.error('Global error:', event.error);\n});\n\nwindow.addEventListener('unhandledrejection', (event) => {\n    console.error('Unhandled promise rejection:', event.reason);\n});\n\n// 导出全局函数供调试使用\nwindow.SPACE2Debug = {\n    loadTimeslot: (timeslot) => window.space2App?.loadTimeslot(timeslot),\n    selectSatellite: (satelliteId) => window.space2App?.earthView?.selectSatellite(satelliteId),\n    clearConnections: () => window.space2App?.earthView?.clearConnections(),\n    getStats: () => ({\n        satellites: window.space2App?.earthView?.satellites?.size || 0,\n        connections: window.space2App?.earthView?.connectionLines?.size || 0,\n        selectedSatellite: window.space2App?.earthView?.getSelectedSatellite()\n    })\n};"