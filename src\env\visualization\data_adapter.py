import numpy as np
import logging
from typing import Dict, List, Optional, Tuple, Any
import yaml
from pathlib import Path

# Use absolute imports as per coding standard #1
from src.env.physics_layer.orbital import OrbitalUpdater
from src.env.physics_layer.communication_refactored import CommunicationManager
from src.env.Foundation_Layer.time_manager import TimeManager


class VisualizationDataAdapter:
    """
    可视化数据适配器
    
    将orbital和communication模块的数据转换为可视化友好的格式
    支持卫星损坏状态处理
    """
    
    def __init__(self, config_file: str = None):
        """
        初始化数据适配器
        
        Args:
            config_file: 配置文件路径
        """
        # 设置配置文件路径
        current_dir = Path(__file__).parent
        physics_dir = current_dir.parent / "physics_layer"
        self.config_file = config_file or str(physics_dir / "config.yaml")
        
        # 加载配置
        self.config = self._load_config()
        
        # 初始化核心模块
        self.orbital_updater = OrbitalUpdater(config_file=self.config_file)
        self.comm_manager = CommunicationManager(self.orbital_updater)
        
        # 缓存机制
        self._data_cache = {}
        
        logging.info("VisualizationDataAdapter initialized successfully")
    
    def _load_config(self) -> dict:
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logging.critical(f"Failed to load config file: {self.config_file} - {e}")
            raise
    
    def get_global_state(self, timeslot: int) -> Dict[str, Any]:
        """
        获取指定时隙的全局状态
        
        Args:
            timeslot: 时间步
            
        Returns:
            包含所有卫星、地面站、云中心位置的字典
        """
        cache_key = f"global_state_{timeslot}"
        if cache_key in self._data_cache:
            return self._data_cache[cache_key]
        
        # 获取所有卫星位置（包括损坏状态）
        satellites = self.orbital_updater.get_satellites_at_time(timeslot)
        satellite_list = []
        
        for sat_id, sat in satellites.items():
            satellite_data = {
                'id': str(sat_id),
                'latitude': float(sat.latitude),
                'longitude': float(sat.longitude),
                'illuminated': bool(sat.illuminated),
                'state': bool(sat.state),  # True=正常, False=损坏
                'status': 'normal' if sat.state else 'damaged'
            }
            satellite_list.append(satellite_data)
        
        # 获取地面站位置
        ground_stations = []
        for gs_id, gs in self.orbital_updater.ground_stations.items():
            ground_stations.append({
                'id': str(gs_id),
                'latitude': float(gs.latitude),
                'longitude': float(gs.longitude),
                'name': str(gs.name or f"Ground_{gs_id}"),
                'type': str(gs.station_type or 'ground')
            })
        
        # 获取云中心位置  
        cloud_centers = []
        for cc_id, cc in self.orbital_updater.cloud_stations.items():
            cloud_centers.append({
                'id': str(cc_id),
                'latitude': float(cc.latitude),
                'longitude': float(cc.longitude),
                'name': str(cc.name or f"Cloud_{cc_id}")
            })
        
        result = {
            'timeslot': int(timeslot),
            'satellites': satellite_list,
            'ground_stations': ground_stations,
            'cloud_centers': cloud_centers,
            'total_satellites': int(len(satellite_list)),
            'active_satellites': int(len([s for s in satellite_list if s['state']])),
            'damaged_satellites': int(len([s for s in satellite_list if not s['state']]))
        }
        
        self._data_cache[cache_key] = result
        return result
    
    def get_satellite_connections(self, satellite_id: str, timeslot: int) -> Dict[str, Any]:
        """
        获取特定卫星的连接信息
        
        Args:
            satellite_id: 卫星ID
            timeslot: 时间步
            
        Returns:
            包含卫星连接、地面站连接、云中心连接的字典
        """
        # 检查卫星是否存在且正常
        satellites = self.orbital_updater.get_satellites_at_time(timeslot)
        if satellite_id not in satellites:
            return {'error': 'Satellite not found'}
        
        satellite = satellites[satellite_id]
        if not satellite.state:
            # 卫星损坏，返回空连接
            return {
                'satellite_id': satellite_id,
                'timeslot': timeslot,
                'satellite_connections': [],
                'ground_connections': [],
                'cloud_connections': [],
                'connection_summary': {
                    'total_satellite_links': 0,
                    'total_ground_links': 0,
                    'total_cloud_links': 0,
                    'status': 'damaged'
                }
            }
        
        # 获取卫星间连接
        try:
            isl_comm = self.comm_manager.get_isl_communication_matrix(timeslot)
            satellite_connections = self._extract_satellite_connections(satellite_id, isl_comm)
        except Exception as e:
            logging.error(f"Failed to get ISL communications: {e}")
            satellite_connections = []
        
        # 获取地面站连接
        try:
            ground_comm = self.comm_manager.get_satellite_ground_communication_matrix(timeslot)
            ground_connections = self._extract_ground_connections(satellite_id, ground_comm)
        except Exception as e:
            logging.error(f"Failed to get ground communications: {e}")
            ground_connections = []
        
        # 获取云中心连接
        try:
            cloud_comm = self.comm_manager.get_satellite_cloud_communication_matrix(timeslot)
            cloud_connections = self._extract_cloud_connections(satellite_id, cloud_comm)
        except Exception as e:
            logging.error(f"Failed to get cloud communications: {e}")
            cloud_connections = []
        
        return {
            'satellite_id': satellite_id,
            'timeslot': timeslot,
            'satellite_connections': satellite_connections,
            'ground_connections': ground_connections,
            'cloud_connections': cloud_connections,
            'connection_summary': {
                'total_satellite_links': len(satellite_connections),
                'total_ground_links': len(ground_connections),
                'total_cloud_links': len(cloud_connections),
                'status': 'normal'
            }
        }
    
    def _extract_satellite_connections(self, satellite_id: str, isl_comm: Dict) -> List[Dict]:
        """提取卫星间连接信息"""
        connections = []
        satellite_ids = isl_comm['satellite_ids']
        
        if satellite_id not in satellite_ids:
            return connections
        
        sat_index = satellite_ids.index(satellite_id)
        visibility_matrix = isl_comm['visibility']
        data_rate_matrix = isl_comm['data_rate_bps']
        distance_matrix = isl_comm['distance_km']
        
        # 检查目标卫星状态，排除损坏的卫星
        satellites = self.orbital_updater.get_satellites_at_time(isl_comm.get('timeslot', 0))
        
        for target_index, target_id in enumerate(satellite_ids):
            if sat_index != target_index and visibility_matrix[sat_index][target_index]:
                # 检查目标卫星是否损坏
                target_satellite = satellites.get(target_id)
                if target_satellite and target_satellite.state:  # 只有正常卫星才显示连接
                    connections.append({
                        'target_id': target_id,
                        'distance_km': float(distance_matrix[sat_index][target_index]),
                        'data_rate_bps': float(data_rate_matrix[sat_index][target_index]),
                        'data_rate_gbps': float(data_rate_matrix[sat_index][target_index]) / 1e9,
                        'link_quality': self._calculate_link_quality(distance_matrix[sat_index][target_index]),
                        'connection_type': 'satellite'
                    })
        
        return connections
    
    def _extract_ground_connections(self, satellite_id: str, ground_comm: Dict) -> List[Dict]:
        """提取地面站连接信息"""
        connections = []
        satellite_ids = ground_comm['satellite_ids']
        
        if satellite_id not in satellite_ids:
            return connections
        
        sat_index = satellite_ids.index(satellite_id)
        visibility_matrix = ground_comm['visibility']
        uplink_rate_matrix = ground_comm['uplink_data_rate_bps']
        downlink_rate_matrix = ground_comm['downlink_data_rate_bps']
        distance_matrix = ground_comm['distance_km']
        ground_station_ids = ground_comm['ground_station_ids']
        
        for gs_index, gs_id in enumerate(ground_station_ids):
            if visibility_matrix[sat_index][gs_index]:
                connections.append({
                    'target_id': gs_id,
                    'distance_km': float(distance_matrix[sat_index][gs_index]),
                    'uplink_rate_bps': float(uplink_rate_matrix[sat_index][gs_index]),
                    'downlink_rate_bps': float(downlink_rate_matrix[sat_index][gs_index]),
                    'uplink_rate_mbps': float(uplink_rate_matrix[sat_index][gs_index]) / 1e6,
                    'downlink_rate_mbps': float(downlink_rate_matrix[sat_index][gs_index]) / 1e6,
                    'link_quality': self._calculate_link_quality(distance_matrix[sat_index][gs_index]),
                    'connection_type': 'ground'
                })
        
        return connections
    
    def _extract_cloud_connections(self, satellite_id: str, cloud_comm: Dict) -> List[Dict]:
        """提取云中心连接信息"""
        connections = []
        satellite_ids = cloud_comm['satellite_ids']
        
        if satellite_id not in satellite_ids:
            return connections
        
        sat_index = satellite_ids.index(satellite_id)
        visibility_matrix = cloud_comm['visibility']
        uplink_rate_matrix = cloud_comm['uplink_data_rate_bps']
        downlink_rate_matrix = cloud_comm['downlink_data_rate_bps']
        distance_matrix = cloud_comm['distance_km']
        cloud_station_ids = cloud_comm['cloud_station_ids']
        
        for cc_index, cc_id in enumerate(cloud_station_ids):
            if visibility_matrix[sat_index][cc_index]:
                connections.append({
                    'target_id': cc_id,
                    'distance_km': float(distance_matrix[sat_index][cc_index]),
                    'uplink_rate_bps': float(uplink_rate_matrix[sat_index][cc_index]),
                    'downlink_rate_bps': float(downlink_rate_matrix[sat_index][cc_index]),
                    'uplink_rate_mbps': float(uplink_rate_matrix[sat_index][cc_index]) / 1e6,
                    'downlink_rate_mbps': float(downlink_rate_matrix[sat_index][cc_index]) / 1e6,
                    'link_quality': self._calculate_link_quality(distance_matrix[sat_index][cc_index]),
                    'connection_type': 'cloud'
                })
        
        return connections
    
    def _calculate_link_quality(self, distance_km: float) -> str:
        """根据距离计算链路质量"""
        if distance_km < 1000:
            return 'excellent'
        elif distance_km < 2000:
            return 'good'
        elif distance_km < 3000:
            return 'fair'
        else:
            return 'poor'
    
    def get_satellite_details(self, satellite_id: str, timeslot: int) -> Dict[str, Any]:
        """
        获取卫星详细信息
        
        Args:
            satellite_id: 卫星ID
            timeslot: 时间步
            
        Returns:
            包含卫星详细信息的字典
        """
        # 基础信息
        satellites = self.orbital_updater.get_satellites_at_time(timeslot)
        if satellite_id not in satellites:
            return {'error': 'Satellite not found'}
        
        sat = satellites[satellite_id]
        
        # 基本信息
        basic_info = {
            'satellite_id': satellite_id,
            'latitude': sat.latitude,
            'longitude': sat.longitude,
            'illuminated': sat.illuminated,
            'state': sat.state,
            'status': 'normal' if sat.state else 'damaged',
            'altitude_km': self.config['system']['leo_altitude_m'] / 1000,
            'light_status': 'Light' if sat.illuminated else 'Shadow'
        }
        
        # 如果卫星损坏，返回最小信息
        if not sat.state:
            return {
                'basic_info': basic_info,
                'connections': {
                    'satellite_count': 0,
                    'ground_count': 0,
                    'cloud_count': 0,
                    'total_bandwidth_gbps': 0,
                    'status': 'damaged'
                },
                'tasks': [],
                'performance': {
                    'energy_level': 0,
                    'cpu_usage': 0,
                    'memory_usage': 0,
                    'status': 'offline'
                }
            }
        
        # 连接统计
        connection_data = self.get_satellite_connections(satellite_id, timeslot)
        connections = {
            'satellite_count': len(connection_data.get('satellite_connections', [])),
            'ground_count': len(connection_data.get('ground_connections', [])),
            'cloud_count': len(connection_data.get('cloud_connections', [])),
            'total_bandwidth_gbps': self._calculate_total_bandwidth(connection_data),
            'status': 'normal'
        }
        
        # 模拟任务信息（后续可集成真实任务模块）
        tasks = self._get_mock_satellite_tasks(satellite_id, timeslot)
        
        # 模拟性能指标
        performance = self._get_mock_performance_metrics(satellite_id, timeslot)
        
        return {
            'basic_info': basic_info,
            'connections': connections,
            'tasks': tasks,
            'performance': performance
        }
    
    def _calculate_total_bandwidth(self, connection_data: Dict) -> float:
        """计算总带宽"""
        total_bandwidth = 0
        
        # 卫星间连接带宽
        for conn in connection_data.get('satellite_connections', []):
            total_bandwidth += conn.get('data_rate_gbps', 0)
        
        # 地面站连接带宽（取上下行较小值）
        for conn in connection_data.get('ground_connections', []):
            uplink_gbps = conn.get('uplink_rate_mbps', 0) / 1000
            downlink_gbps = conn.get('downlink_rate_mbps', 0) / 1000
            total_bandwidth += min(uplink_gbps, downlink_gbps)
        
        # 云中心连接带宽（取上下行较小值）
        for conn in connection_data.get('cloud_connections', []):
            uplink_gbps = conn.get('uplink_rate_mbps', 0) / 1000
            downlink_gbps = conn.get('downlink_rate_mbps', 0) / 1000
            total_bandwidth += min(uplink_gbps, downlink_gbps)
        
        return round(total_bandwidth, 3)
    
    def _get_mock_satellite_tasks(self, satellite_id: str, timeslot: int) -> List[Dict]:
        """获取模拟任务数据"""
        # 模拟一些任务数据用于展示
        import random
        random.seed(hash(f"{satellite_id}_{timeslot}"))  # 确保数据一致性
        
        task_count = random.randint(0, 5)
        tasks = []
        
        for i in range(task_count):
            status_choices = ['processing', 'queued', 'completed']
            status = random.choice(status_choices)
            progress = random.randint(10, 100) if status == 'processing' else (100 if status == 'completed' else 0)
            
            tasks.append({
                'task_id': f'task_{satellite_id}_{timeslot:03d}_{i:02d}',
                'status': status,
                'progress': progress,
                'data_size_mb': round(random.uniform(50, 500), 1),
                'priority': random.choice(['high', 'medium', 'low']),
                'deadline': timeslot + random.randint(10, 50),
                'source': f'ground_{random.randint(1, 420)}',
                'estimated_completion': timeslot + random.randint(1, 10)
            })
        
        return tasks
    
    def _get_mock_performance_metrics(self, satellite_id: str, timeslot: int) -> Dict:
        """获取模拟性能指标"""
        import random
        random.seed(hash(f"{satellite_id}_{timeslot}_perf"))
        
        return {
            'energy_level': random.randint(60, 95),
            'cpu_usage': random.randint(30, 85),
            'memory_usage': random.randint(40, 80),
            'temperature': random.randint(-10, 40),
            'status': 'normal'
        }
    
    def clear_cache(self):
        """清除缓存"""
        self._data_cache.clear()
        if hasattr(self.comm_manager, 'clear_cache'):
            self.comm_manager.clear_cache()
        logging.info("VisualizationDataAdapter cache cleared")
    
    def get_system_statistics(self, timeslot: int) -> Dict[str, Any]:
        """获取系统统计信息"""
        global_state = self.get_global_state(timeslot)
        
        return {
            'timeslot': timeslot,
            'total_satellites': global_state['total_satellites'],
            'active_satellites': global_state['active_satellites'],
            'damaged_satellites': global_state['damaged_satellites'],
            'ground_stations': len(global_state['ground_stations']),
            'cloud_centers': len(global_state['cloud_centers']),
            'system_health': global_state['active_satellites'] / global_state['total_satellites'] if global_state['total_satellites'] > 0 else 0
        }