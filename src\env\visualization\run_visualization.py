#!/usr/bin/env python3
"""
SPACE2 Visualization Launcher
启动SPACE2卫星星座可视化系统
"""

import sys
import os
import argparse
import webbrowser
import time
import subprocess
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.env.visualization.api_server import VisualizationAPI


def check_dependencies():
    """检查依赖包"""
    required_packages = [
        ('flask', 'flask'),
        ('flask-cors', 'flask_cors'),
        ('numpy', 'numpy'),
        ('pandas', 'pandas'),
        ('pyyaml', 'yaml')
    ]
    
    missing_packages = []
    
    for package_name, import_name in required_packages:
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(package_name)
    
    if missing_packages:
        print("Missing required packages:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\nInstall missing packages with:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True


def check_data_files():
    """检查数据文件是否存在"""
    env_data_dir = Path(__file__).parent.parent / "env_data"
    
    required_files = [
        "satellite_data72_1.csv",
        "global_ground_stations.csv", 
        "cloud_station.csv"
    ]
    
    missing_files = []
    
    for filename in required_files:
        file_path = env_data_dir / filename
        if not file_path.exists():
            missing_files.append(str(file_path))
    
    if missing_files:
        print("Missing required data files:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        return False
    
    return True


def open_browser(url, delay=3):
    """延迟打开浏览器"""
    def delayed_open():
        time.sleep(delay)
        try:
            webbrowser.open(url)
            print(f"Opened browser: {url}")
        except Exception as e:
            print(f"Failed to open browser: {e}")
            print(f"Please manually open: {url}")
    
    import threading
    thread = threading.Thread(target=delayed_open)
    thread.daemon = True
    thread.start()


def main():
    parser = argparse.ArgumentParser(
        description='SPACE2 Satellite Constellation Visualization',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_visualization.py                    # Default: localhost:5000
  python run_visualization.py --host 0.0.0.0    # Allow external access
  python run_visualization.py --port 8080       # Use port 8080
  python run_visualization.py --no-browser      # Don't open browser automatically
  python run_visualization.py --debug           # Enable debug mode
        """
    )
    
    parser.add_argument('--host', default='127.0.0.1', 
                       help='Host address (default: 127.0.0.1)')
    parser.add_argument('--port', type=int, default=5000,
                       help='Port number (default: 5000)')
    parser.add_argument('--debug', action='store_true',
                       help='Enable debug mode')
    parser.add_argument('--no-browser', action='store_true',
                       help="Don't open browser automatically")
    parser.add_argument('--config', 
                       help='Path to config file (default: auto-detect)')
    parser.add_argument('--check-only', action='store_true',
                       help='Only check dependencies and data files')
    
    args = parser.parse_args()
    
    print("SPACE2 Satellite Constellation Visualization")
    print("=" * 50)
    
    # 检查依赖
    print("Checking dependencies...")
    if not check_dependencies():
        sys.exit(1)
    print("+ Dependencies OK")
    
    # 检查数据文件
    print("Checking data files...")
    if not check_data_files():
        sys.exit(1)
    print("+ Data files OK")
    
    if args.check_only:
        print("+ All checks passed!")
        return
    
    # 构建URL
    url = f"http://{args.host}:{args.port}"
    
    print(f"\nStarting visualization server...")
    print(f"Host: {args.host}")
    print(f"Port: {args.port}")
    print(f"URL: {url}")
    print(f"Debug: {args.debug}")
    
    # 自动打开浏览器
    if not args.no_browser:
        print(f"\nBrowser will open automatically in 3 seconds...")
        open_browser(url)
    else:
        print(f"\nPlease open your browser and navigate to: {url}")
    
    print(f"\nPress Ctrl+C to stop the server")
    print("=" * 50)
    
    try:
        # 创建并启动API服务器
        api_server = VisualizationAPI(
            config_file=args.config,
            host=args.host,
            port=args.port
        )
        
        api_server.run(debug=args.debug)
        
    except KeyboardInterrupt:
        print("\n\nServer stopped by user")
    except Exception as e:
        print(f"\nError: {e}")
        print("\nTroubleshooting:")
        print("1. Check if the port is already in use")
        print("2. Verify all dependencies are installed")
        print("3. Check data files exist")
        print("4. Run with --debug for more information")
        sys.exit(1)


if __name__ == '__main__':
    main()