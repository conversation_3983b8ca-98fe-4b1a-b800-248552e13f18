from flask import Flask, jsonify, request, send_from_directory
from flask_cors import CORS
import logging
import traceback
from pathlib import Path
import sys
import os

# Add project root to path for absolute imports
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# Use absolute imports as per coding standard #1
from src.env.visualization.data_adapter import VisualizationDataAdapter
from src.env.Foundation_Layer.logging_config import initialize_logging_and_config


class VisualizationAPI:
    """
    SPACE2可视化API服务器
    
    提供RESTful API接口供前端调用，获取卫星仿真数据
    """
    
    def __init__(self, config_file: str = None, host: str = '127.0.0.1', port: int = 5000):
        """
        初始化API服务器
        
        Args:
            config_file: 配置文件路径
            host: 服务器主机地址
            port: 服务器端口
        """
        self.app = Flask(__name__)
        CORS(self.app)  # 允许跨域请求
        
        self.host = host
        self.port = port
        
        # 初始化日志
        try:
            initialize_logging_and_config(config_file)
        except Exception as e:
            print(f"Warning: Failed to initialize logging: {e}")
        
        # 初始化数据适配器
        try:
            self.data_adapter = VisualizationDataAdapter(config_file)
            logging.info("VisualizationAPI initialized successfully")
        except Exception as e:
            logging.error(f"Failed to initialize VisualizationDataAdapter: {e}")
            raise
        
        # 设置路由
        self.setup_routes()
        
        # 设置错误处理
        self.setup_error_handlers()
    
    def setup_routes(self):
        """设置API路由"""
        
        @self.app.route('/')
        def index():
            """主页面"""
            return send_from_directory('static', 'index.html')
        
        @self.app.route('/static/<path:filename>')
        def static_files(filename):
            """静态文件服务"""
            static_dir = Path(__file__).parent / 'static'
            return send_from_directory(str(static_dir), filename)
        
        @self.app.route('/api/health')
        def health_check():
            """健康检查"""
            return jsonify({
                'status': 'healthy',
                'service': 'SPACE2 Visualization API',
                'version': '1.0.0'
            })
        
        @self.app.route('/api/timeslot/<int:timeslot>/global_state')
        def get_global_state(timeslot):
            """
            获取全局状态：所有卫星、地面站、云中心位置
            
            Args:
                timeslot: 时间步 (0-1441)
                
            Returns:
                JSON响应包含所有节点位置和状态
            """
            try:
                # 验证时隙范围
                if not 0 <= timeslot <= 1441:
                    return jsonify({'error': 'Timeslot must be between 0 and 1441'}), 400
                
                result = self.data_adapter.get_global_state(timeslot)
                return jsonify(result)
                
            except Exception as e:
                logging.error(f"Error in get_global_state: {e}")
                logging.error(traceback.format_exc())
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/satellite/<satellite_id>/connections/<int:timeslot>')
        def get_satellite_connections(satellite_id, timeslot):
            """
            获取特定卫星的连接信息
            
            Args:
                satellite_id: 卫星ID
                timeslot: 时间步
                
            Returns:
                JSON响应包含卫星的所有连接关系
            """
            try:
                # 验证时隙范围
                if not 0 <= timeslot <= 1441:
                    return jsonify({'error': 'Timeslot must be between 0 and 1441'}), 400
                
                result = self.data_adapter.get_satellite_connections(satellite_id, timeslot)
                
                if 'error' in result:
                    return jsonify(result), 404
                
                return jsonify(result)
                
            except Exception as e:
                logging.error(f"Error in get_satellite_connections: {e}")
                logging.error(traceback.format_exc())
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/satellite/<satellite_id>/details/<int:timeslot>')
        def get_satellite_details(satellite_id, timeslot):
            """
            获取卫星详细信息：位置、状态、任务、性能
            
            Args:
                satellite_id: 卫星ID
                timeslot: 时间步
                
            Returns:
                JSON响应包含卫星详细信息
            """
            try:
                # 验证时隙范围
                if not 0 <= timeslot <= 1441:
                    return jsonify({'error': 'Timeslot must be between 0 and 1441'}), 400
                
                result = self.data_adapter.get_satellite_details(satellite_id, timeslot)
                
                if 'error' in result:
                    return jsonify(result), 404
                
                return jsonify(result)
                
            except Exception as e:
                logging.error(f"Error in get_satellite_details: {e}")
                logging.error(traceback.format_exc())
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/system/statistics/<int:timeslot>')
        def get_system_statistics(timeslot):
            """
            获取系统统计信息
            
            Args:
                timeslot: 时间步
                
            Returns:
                JSON响应包含系统整体统计
            """
            try:
                # 验证时隙范围
                if not 0 <= timeslot <= 1441:
                    return jsonify({'error': 'Timeslot must be between 0 and 1441'}), 400
                
                result = self.data_adapter.get_system_statistics(timeslot)
                return jsonify(result)
                
            except Exception as e:
                logging.error(f"Error in get_system_statistics: {e}")
                logging.error(traceback.format_exc())
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/cache/clear', methods=['POST'])
        def clear_cache():
            """清除缓存"""
            try:
                self.data_adapter.clear_cache()
                return jsonify({'status': 'success', 'message': 'Cache cleared'})
            except Exception as e:
                logging.error(f"Error in clear_cache: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/timeslots/range')
        def get_timeslot_range():
            """获取有效时隙范围"""
            try:
                total_timeslots = self.data_adapter.orbital_updater.get_total_timeslots()
                return jsonify({
                    'min_timeslot': 0,
                    'max_timeslot': total_timeslots - 1,
                    'total_timeslots': total_timeslots
                })
            except Exception as e:
                logging.error(f"Error in get_timeslot_range: {e}")
                return jsonify({'error': str(e)}), 500
    
    def setup_error_handlers(self):
        """设置错误处理器"""
        
        @self.app.errorhandler(404)
        def not_found(error):
            return jsonify({'error': 'Endpoint not found'}), 404
        
        @self.app.errorhandler(500)
        def internal_error(error):
            logging.error(f"Internal server error: {error}")
            return jsonify({'error': 'Internal server error'}), 500
        
        @self.app.errorhandler(Exception)
        def handle_exception(e):
            logging.error(f"Unhandled exception: {e}")
            logging.error(traceback.format_exc())
            return jsonify({'error': 'Unexpected error occurred'}), 500
    
    def run(self, debug: bool = False):
        """
        启动API服务器
        
        Args:
            debug: 是否启用调试模式
        """
        try:
            logging.info(f"Starting SPACE2 Visualization API server on {self.host}:{self.port}")
            print(f"SPACE2 Visualization API server starting...")
            print(f"Open your browser and go to: http://{self.host}:{self.port}")
            
            self.app.run(
                host=self.host,
                port=self.port,
                debug=debug,
                threaded=True
            )
        except Exception as e:
            logging.error(f"Failed to start server: {e}")
            raise


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='SPACE2 Visualization API Server')
    parser.add_argument('--host', default='127.0.0.1', help='Host address (default: 127.0.0.1)')
    parser.add_argument('--port', type=int, default=5000, help='Port number (default: 5000)')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    parser.add_argument('--config', help='Config file path')
    
    args = parser.parse_args()
    
    try:
        # 创建API服务器实例
        api_server = VisualizationAPI(
            config_file=args.config,
            host=args.host,
            port=args.port
        )
        
        # 启动服务器
        api_server.run(debug=args.debug)
        
    except KeyboardInterrupt:
        print("\nServer stopped by user")
    except Exception as e:
        print(f"Error starting server: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()