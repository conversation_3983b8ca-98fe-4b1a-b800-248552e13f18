2025-08-16 15:32:59 [    INFO] root: 日志系统初始化完成
2025-08-16 15:32:59 [    INFO] root: 日志和配置系统初始化完成
2025-08-16 15:32:59 [    INFO] src.env.Foundation_Layer.time_manager: 时间管理器初始化 - 开始时间: 2025-06-08 04:00:00, 时隙时长: 5s, 总时隙: 1441
2025-08-16 15:32:59 [    INFO] root: Loaded 103752 satellite records with 1441 timestamps
2025-08-16 15:32:59 [    INFO] root: Successfully loaded 420 ground stations
2025-08-16 15:32:59 [    INFO] root: Successfully loaded 5 cloud centers
2025-08-16 15:32:59 [    INFO] root: Communication Manager initialized successfully
2025-08-16 15:32:59 [    INFO] root: VisualizationDataAdapter initialized successfully
2025-08-16 15:32:59 [    INFO] root: VisualizationAPI initialized successfully
2025-08-16 15:32:59 [    INFO] root: Starting SPACE2 Visualization API server on 127.0.0.1:5000
2025-08-16 15:32:59 [    INFO] werkzeug: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-08-16 15:32:59 [    INFO] werkzeug: [33mPress CTRL+C to quit[0m
2025-08-16 15:33:03 [    INFO] werkzeug: 127.0.0.1 - - [16/Aug/2025 15:33:03] "GET / HTTP/1.1" 200 -
2025-08-16 15:33:03 [    INFO] werkzeug: 127.0.0.1 - - [16/Aug/2025 15:33:03] "[33mGET /css/style.css HTTP/1.1[0m" 404 -
2025-08-16 15:33:03 [    INFO] werkzeug: 127.0.0.1 - - [16/Aug/2025 15:33:03] "[33mGET /js/earth-view.js HTTP/1.1[0m" 404 -
2025-08-16 15:33:03 [    INFO] werkzeug: 127.0.0.1 - - [16/Aug/2025 15:33:03] "[33mGET /js/details-panel.js HTTP/1.1[0m" 404 -
2025-08-16 15:33:03 [    INFO] werkzeug: 127.0.0.1 - - [16/Aug/2025 15:33:03] "[33mGET /js/main.js HTTP/1.1[0m" 404 -
2025-08-16 15:33:10 [    INFO] werkzeug: 127.0.0.1 - - [16/Aug/2025 15:33:10] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-08-16 15:34:03 [    INFO] root: 日志系统初始化完成
2025-08-16 15:34:03 [    INFO] root: 日志和配置系统初始化完成
2025-08-16 15:34:03 [    INFO] src.env.Foundation_Layer.time_manager: 时间管理器初始化 - 开始时间: 2025-06-08 04:00:00, 时隙时长: 5s, 总时隙: 1441
2025-08-16 15:34:03 [    INFO] root: Loaded 103752 satellite records with 1441 timestamps
2025-08-16 15:34:03 [    INFO] root: Successfully loaded 420 ground stations
2025-08-16 15:34:03 [    INFO] root: Successfully loaded 5 cloud centers
2025-08-16 15:34:03 [    INFO] root: Communication Manager initialized successfully
2025-08-16 15:34:03 [    INFO] root: VisualizationDataAdapter initialized successfully
2025-08-16 15:34:03 [    INFO] root: VisualizationAPI initialized successfully
2025-08-16 15:34:03 [    INFO] root: Starting SPACE2 Visualization API server on 127.0.0.1:5000
2025-08-16 15:34:03 [    INFO] werkzeug: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-08-16 15:34:03 [    INFO] werkzeug: [33mPress CTRL+C to quit[0m
2025-08-16 15:34:06 [    INFO] werkzeug: 127.0.0.1 - - [16/Aug/2025 15:34:06] "[36mGET / HTTP/1.1[0m" 304 -
2025-08-16 15:34:07 [    INFO] werkzeug: 127.0.0.1 - - [16/Aug/2025 15:34:07] "[33mGET /css/style.css HTTP/1.1[0m" 404 -
2025-08-16 15:34:07 [    INFO] werkzeug: 127.0.0.1 - - [16/Aug/2025 15:34:07] "[33mGET /js/earth-view.js HTTP/1.1[0m" 404 -
2025-08-16 15:34:07 [    INFO] werkzeug: 127.0.0.1 - - [16/Aug/2025 15:34:07] "[33mGET /js/details-panel.js HTTP/1.1[0m" 404 -
2025-08-16 15:34:07 [    INFO] werkzeug: 127.0.0.1 - - [16/Aug/2025 15:34:07] "[33mGET /js/main.js HTTP/1.1[0m" 404 -
2025-08-16 16:06:46 [    INFO] root: 日志系统初始化完成
2025-08-16 16:06:46 [    INFO] root: 日志和配置系统初始化完成
2025-08-16 16:06:46 [    INFO] src.env.physics_layer.load_balancing: 负载均衡计算器初始化完成，加载24个区域定义
2025-08-16 16:07:13 [    INFO] root: 日志系统初始化完成
2025-08-16 16:07:13 [    INFO] root: 日志和配置系统初始化完成
2025-08-16 16:07:13 [    INFO] src.env.physics_layer.load_balancing: 负载均衡计算器初始化完成，加载24个区域定义
2025-08-16 16:07:13 [    INFO] src.env.Foundation_Layer.time_manager: 时间管理器初始化 - 开始时间: 2025-06-08 04:00:00, 时隙时长: 5s, 总时隙: 1441
2025-08-16 16:07:13 [    INFO] src.env.physics_layer.load_balancing: 时隙100负载均衡计算完成: 10个活跃区域, 平均方差=0.000000
2025-08-16 16:07:13 [    INFO] src.env.physics_layer.load_balancing: 时隙200负载均衡计算完成: 10个活跃区域, 平均方差=0.000000
2025-08-16 16:08:01 [    INFO] root: 日志系统初始化完成
2025-08-16 16:08:01 [    INFO] root: 日志和配置系统初始化完成
2025-08-16 16:08:01 [    INFO] src.env.physics_layer.load_balancing: 负载均衡计算器初始化完成，加载24个区域定义
2025-08-16 16:08:01 [    INFO] src.env.Foundation_Layer.time_manager: 时间管理器初始化 - 开始时间: 2025-06-08 04:00:00, 时隙时长: 5s, 总时隙: 1441
2025-08-16 16:08:01 [    INFO] src.env.physics_layer.load_balancing: 时隙100负载均衡计算完成: 10个活跃区域, 平均方差=0.000000
2025-08-16 16:08:01 [    INFO] src.env.physics_layer.load_balancing: 时隙200负载均衡计算完成: 10个活跃区域, 平均方差=0.000000
2025-08-16 16:08:01 [    INFO] src.env.physics_layer.load_balancing: 时隙1负载均衡计算完成: 10个活跃区域, 平均方差=0.000000
2025-08-16 16:08:01 [    INFO] src.env.physics_layer.load_balancing: 时隙100负载均衡计算完成: 10个活跃区域, 平均方差=0.000000
2025-08-16 16:08:01 [    INFO] src.env.physics_layer.load_balancing: 时隙500负载均衡计算完成: 10个活跃区域, 平均方差=0.000000
2025-08-16 16:08:01 [    INFO] src.env.physics_layer.load_balancing: 时隙1000负载均衡计算完成: 10个活跃区域, 平均方差=0.000000
2025-08-16 16:08:20 [    INFO] root: 日志系统初始化完成
2025-08-16 16:08:20 [    INFO] root: 日志和配置系统初始化完成
2025-08-16 16:08:20 [    INFO] src.env.physics_layer.load_balancing: 负载均衡计算器初始化完成，加载24个区域定义
2025-08-16 16:08:20 [    INFO] src.env.Foundation_Layer.time_manager: 时间管理器初始化 - 开始时间: 2025-06-08 04:00:00, 时隙时长: 5s, 总时隙: 1441
2025-08-16 16:08:20 [    INFO] src.env.physics_layer.load_balancing: 时隙100负载均衡计算完成: 10个活跃区域, 平均方差=0.000000
2025-08-16 16:08:20 [    INFO] src.env.physics_layer.load_balancing: 时隙200负载均衡计算完成: 10个活跃区域, 平均方差=0.000000
2025-08-16 16:08:20 [    INFO] src.env.physics_layer.load_balancing: 时隙1负载均衡计算完成: 10个活跃区域, 平均方差=0.000000
2025-08-16 16:08:20 [    INFO] src.env.physics_layer.load_balancing: 时隙100负载均衡计算完成: 10个活跃区域, 平均方差=0.000000
2025-08-16 16:08:20 [    INFO] src.env.physics_layer.load_balancing: 时隙500负载均衡计算完成: 10个活跃区域, 平均方差=0.000000
2025-08-16 16:08:20 [    INFO] src.env.physics_layer.load_balancing: 时隙1000负载均衡计算完成: 10个活跃区域, 平均方差=0.000000
2025-08-16 16:08:20 [    INFO] src.env.physics_layer.load_balancing: 时隙1440负载均衡计算完成: 10个活跃区域, 平均方差=0.000000
2025-08-16 16:08:20 [    INFO] src.env.physics_layer.load_balancing: 时隙1负载均衡计算完成: 0个活跃区域, 平均方差=nan
2025-08-16 16:08:20 [    INFO] src.env.physics_layer.load_balancing: 时隙1负载均衡计算完成: 1个活跃区域, 平均方差=0.000000
2025-08-16 16:08:20 [    INFO] src.env.physics_layer.load_balancing: 时隙1负载均衡计算完成: 2个活跃区域, 平均方差=0.000000
2025-08-17 15:08:27 [    INFO] root: 日志系统初始化完成
2025-08-17 15:08:27 [    INFO] root: 日志和配置系统初始化完成
2025-08-17 15:10:26 [    INFO] root: 日志系统初始化完成
2025-08-17 15:10:26 [    INFO] root: 日志和配置系统初始化完成
2025-08-17 15:10:26 [    INFO] SatelliteCompute_0: Added task 1 to queue (queue size: 1)
2025-08-17 15:10:26 [    INFO] CloudCompute_0: Added task 2 to cloud queue (queue size: 1)
2025-08-17 15:10:26 [    INFO] SatelliteCompute_0: Added task 3 to queue (queue size: 1)
2025-08-17 15:10:26 [    INFO] ComputeManager: Assigned task 3 to satellite 0
2025-08-17 15:10:26 [    INFO] CloudCompute_0: Added task 4 to cloud queue (queue size: 1)
2025-08-17 15:10:26 [    INFO] ComputeManager: Assigned task 4 to cloud 0
2025-08-17 15:10:26 [    INFO] SatelliteCompute_0: Added task 10 to queue (queue size: 1)
2025-08-17 15:10:26 [    INFO] ComputeManager: Assigned task 10 to satellite 0
2025-08-17 15:10:26 [    INFO] CloudCompute_1: Added task 11 to cloud queue (queue size: 1)
2025-08-17 15:10:26 [    INFO] ComputeManager: Assigned task 11 to cloud 1
2025-08-17 15:10:26 [    INFO] SatelliteCompute_2: Added task 12 to queue (queue size: 1)
2025-08-17 15:10:26 [    INFO] ComputeManager: Assigned task 12 to satellite 2
2025-08-17 15:10:26 [    INFO] SatelliteCompute_0: Scheduled task 10 for processing
2025-08-17 15:10:26 [    INFO] SatelliteCompute_2: Scheduled task 12 for processing
2025-08-17 15:10:26 [    INFO] CloudCompute_0: Processed 0 tasks, 0 still processing, 0 in queue
2025-08-17 15:10:26 [    INFO] CloudCompute_1: Processed 1 tasks, 0 still processing, 0 in queue
2025-08-17 15:10:26 [    INFO] CloudCompute_2: Processed 0 tasks, 0 still processing, 0 in queue
2025-08-17 15:10:26 [    INFO] CloudCompute_3: Processed 0 tasks, 0 still processing, 0 in queue
2025-08-17 15:10:26 [    INFO] CloudCompute_4: Processed 0 tasks, 0 still processing, 0 in queue
