2025-08-16 15:33:57 [    INFO] root: 日志系统初始化完成
2025-08-16 15:33:57 [    INFO] root: 日志和配置系统初始化完成
2025-08-16 15:33:57 [    INFO] src.env.Foundation_Layer.time_manager: 时间管理器初始化 - 开始时间: 2025-06-08 04:00:00, 时隙时长: 5s, 总时隙: 1441
2025-08-16 15:33:57 [    INFO] root: Loaded 103752 satellite records with 1441 timestamps
2025-08-16 15:33:57 [    INFO] root: Successfully loaded 420 ground stations
2025-08-16 15:33:57 [    INFO] root: Successfully loaded 5 cloud centers
2025-08-16 15:33:57 [    INFO] root: Communication Manager initialized successfully
2025-08-16 15:33:57 [    INFO] root: VisualizationDataAdapter initialized successfully
2025-08-16 15:33:57 [    INFO] root: VisualizationAPI initialized successfully
2025-08-16 15:33:57 [    INFO] root: Starting SPACE2 Visualization API server on 127.0.0.1:5001
2025-08-16 15:33:57 [    INFO] werkzeug: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5001
2025-08-16 15:33:57 [    INFO] werkzeug: [33mPress CTRL+C to quit[0m
2025-08-16 15:34:53 [    INFO] werkzeug: 127.0.0.1 - - [16/Aug/2025 15:34:53] "GET /api/health HTTP/1.1" 200 -
2025-08-16 15:35:19 [   ERROR] root: Error in get_global_state: Object of type int64 is not JSON serializable
2025-08-16 15:35:19 [   ERROR] root: Traceback (most recent call last):
  File "D:\paper\space\SPACE2\src\env\visualization\api_server.py", line 100, in get_global_state
    return jsonify(result)
  File "C:\Users\<USER>\.conda\envs\space\lib\site-packages\flask\json\__init__.py", line 170, in jsonify
    return current_app.json.response(*args, **kwargs)  # type: ignore[return-value]
  File "C:\Users\<USER>\.conda\envs\space\lib\site-packages\flask\json\provider.py", line 214, in response
    f"{self.dumps(obj, **dump_args)}\n", mimetype=self.mimetype
  File "C:\Users\<USER>\.conda\envs\space\lib\site-packages\flask\json\provider.py", line 179, in dumps
    return json.dumps(obj, **kwargs)
  File "C:\Users\<USER>\.conda\envs\space\lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
  File "C:\Users\<USER>\.conda\envs\space\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\.conda\envs\space\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\.conda\envs\space\lib\site-packages\flask\json\provider.py", line 121, in _default
    raise TypeError(f"Object of type {type(o).__name__} is not JSON serializable")
TypeError: Object of type int64 is not JSON serializable

2025-08-16 15:35:19 [    INFO] werkzeug: 127.0.0.1 - - [16/Aug/2025 15:35:19] "[35m[1mGET /api/timeslot/0/global_state HTTP/1.1[0m" 500 -
2025-08-16 15:36:20 [    INFO] root: 日志系统初始化完成
2025-08-16 15:36:20 [    INFO] root: 日志和配置系统初始化完成
2025-08-16 15:36:20 [    INFO] src.env.Foundation_Layer.time_manager: 时间管理器初始化 - 开始时间: 2025-06-08 04:00:00, 时隙时长: 5s, 总时隙: 1441
2025-08-16 15:36:20 [    INFO] root: Loaded 103752 satellite records with 1441 timestamps
2025-08-16 15:36:20 [    INFO] root: Successfully loaded 420 ground stations
2025-08-16 15:36:20 [    INFO] root: Successfully loaded 5 cloud centers
2025-08-16 15:36:20 [    INFO] root: Communication Manager initialized successfully
2025-08-16 15:36:20 [    INFO] root: VisualizationDataAdapter initialized successfully
2025-08-16 15:36:20 [    INFO] root: VisualizationAPI initialized successfully
2025-08-16 15:36:20 [    INFO] root: Starting SPACE2 Visualization API server on 127.0.0.1:5001
2025-08-16 15:36:20 [    INFO] werkzeug: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5001
2025-08-16 15:36:20 [    INFO] werkzeug: [33mPress CTRL+C to quit[0m
2025-08-16 15:38:06 [    INFO] werkzeug: 127.0.0.1 - - [16/Aug/2025 15:38:06] "GET /api/timeslot/0/global_state HTTP/1.1" 200 -
2025-08-16 15:38:34 [    INFO] werkzeug: 127.0.0.1 - - [16/Aug/2025 15:38:34] "[33mGET /api/satellite/111/details/0 HTTP/1.1[0m" 404 -
2025-08-16 15:38:50 [    INFO] werkzeug: 127.0.0.1 - - [16/Aug/2025 15:38:50] "GET /api/timeslot/0/global_state HTTP/1.1" 200 -
