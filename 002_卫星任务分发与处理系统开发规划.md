### **科研仿真平台功能开发规划模板 (SPACE2 卫星任务分发与处理系统)**

-----

#### **模块/功能编号:** F02

#### **模块/功能名称:** 卫星任务分发与处理系统集成

### **1. 目标与范围定义 (Objective & Scope Definition)**

  * **核心目标 / 待解决的科研问题 (Core Objective / Research Problem to Solve):**

    > 实现完整的卫星边缘计算任务处理流程，解决多智能体强化学习算法缺乏标准化仿真环境的问题。构建端到端的任务生成、智能分发、处理和结果返回系统，为算法性能评估提供可靠基准。

  * **功能概述 (Feature Overview):**

    > 集成现有的任务生成器、轨道动力学、通信模型和卫星处理模块，构建完整的任务处理管道。系统将支持基于距离最近原则的任务分发、DPSQ动态优先级调度算法、跨时隙任务处理，以及本地对比仿真环境。

  * **关键输入 (Key Inputs):**

    > - 420个地面站的地理位置数据 (global_ground_stations.csv)
    > - 72颗LEO卫星的轨道数据 (satellite_data72_1.csv) 
    > - 系统配置参数 (config.yaml)
    > - 时间上下文 (TimeContext对象)
    > - 动态任务负载 (TaskGenerator生成)

  * **关键输出 (Key Outputs):**

    > - 完整的任务跟踪记录 (TaskTrackingRecord)
    > - 多维度性能指标分析 (AlgorithmResult)
    > - 延迟和能耗统计数据
    > - 卫星vs本地处理对比报告
    > - 系统完成率和资源利用率统计

  * **边界与约束 (Boundaries & Constraints):**

    > 基于现有SPACE2架构，不修改核心轨道动力学和通信模型。所有参数从config.yaml读取，禁止硬编码。支持1441个时隙的完整仿真。遵循CLAUDE.md编程规范，使用绝对导入。保持现有接口向后兼容。

### **2. 技术与交互设计 (Technical & Interaction Design)**

  * **设计思路 / 算法原理 (Design Rationale / Algorithm Principle):**

    > **任务分发算法**: 基于ECEF坐标系的距离计算
    > ```
    > distance = sqrt((x1-x2)² + (y1-y2)² + (z1-z2)²)
    > selected_satellite = argmin(distance[visible_satellites])
    > ```
    > 
    > **DPSQ调度算法**: 动态优先级评分
    > ```
    > Score(T_i, t_now) = w_p * f_p(P_i) + w_d * f_d(D_i, t_now) - w_c * f_c(S_i, C_i)
    > ```
    > 
    > **本地对比算法**: 理想条件处理
    > ```
    > local_time = complexity / local_cpu_frequency
    > # 无通信延迟，无资源竞争，无重传
    > ```

  * **模块交互设计 (Module Interaction Design):**

    > 系统将在现有SPACE2三层架构基础上，增强模块间的集成和数据流管理。

      * **影响的现有模块 (Affected Existing Modules):** 
        - `src/env/task/task_generator.py` (移动到physics_layer并重构)
        - `src/env/task/task_tracking.py` (接口标准化)
        - `src/env/satellite_cloud/satellite.py` (集成验证)
        - `src/env/metrics_model/` (性能分析增强)

      * **新增的模块/类 (New Modules/Classes):** 
        - `src/local/local_task_processor.py` (本地任务处理器)
        - `src/local/simulation_runner.py` (仿真运行控制器)
        - `src/local/results_analyzer.py` (结果分析器)
        - `src/local/metrics_collector.py` (综合指标收集器)

      * **数据交互接口 (Data Interaction Interface):**
        > 主仿真流程的数据流为：
        > 1. `TimeManager.get_context(step)` 为所有模块提供统一时间上下文
        > 2. `TaskDataLoader.get_tasks_at_timeslot(step)` 生成当前时隙任务
        > 3. `TaskTrackingSystem.simulate_step(step)` 执行任务分发和跟踪
        > 4. `OrbitalUpdater.get_visibility_matrices(step)` 提供卫星可见性数据
        > 5. `Satellite.process_timeslot(TimeContext)` 处理分配的任务
        > 6. `AlgorithmMetrics.analyze(results)` 生成性能分析报告

  * **数据结构与状态变量 (Data Structures & State Variables):**

    > **仿真控制器状态**:
    > ```python
    > class SimulationController:
    >     current_step: int                    # 当前仿真时隙
    >     active_tasks: Dict[int, Task]        # 活跃任务字典
    >     satellite_assignments: Dict[int, List[int]]  # 任务-卫星分配映射
    >     performance_records: List[Dict]      # 性能记录列表
    >     simulation_mode: SimulationMode      # 仿真模式枚举
    > ```
    > 
    > **本地处理器状态**:
    > ```python
    > class LocalTaskProcessor:
    >     cpu_frequency: float                 # 本地CPU频率
    >     processing_overhead: float = 0.0     # 处理开销(本地为0)
    >     completed_tasks: List[TaskResult]    # 完成任务列表
    > ```
    > 
    > **配置增强** (config.yaml新增):
    > ```yaml
    > local_simulation:
    >   enable: true
    >   cpu_frequency_hz: 10e9               # 本地CPU频率
    >   processing_mode: "sequential"        # 处理模式
    >   comparison_enabled: true             # 启用对比分析
    > ```

### **3. 验证与评估方案 (Validation & Evaluation Plan)**

  * **正确性验证方案 (Correctness Validation Plan):**

      * **单元验证 (Unit Validation):**
        > 1. **任务生成验证**: 创建测试脚本验证TaskGenerator生成的任务数量符合泊松分布统计特性，不同地理类型的λ参数计算正确。
        > 2. **距离计算验证**: 使用已知坐标的卫星和地面站，手动计算ECEF距离，验证orbital模块的距离计算结果误差在1%以内。
        > 3. **DPSQ算法验证**: 构造已知优先级、截止时间和复杂度的测试任务，验证调度器计算的优先级分数与手动计算一致。
        > 4. **能耗计算验证**: 使用固定的CPU周期数和能效系数，验证能耗计算公式E=zeta*C的实现正确性。

      * **集成验证 (Integration Validation):**
        > 1. **端到端流程验证**: 运行10个时隙的短时仿真，跟踪特定任务从生成到完成的全过程，验证每个阶段的状态转换正确。
        > 2. **最近卫星验证**: 在已知卫星位置的时隙，验证任务确实分配给了距离最近的可见卫星，误差容忍度为5%。
        > 3. **跨时隙一致性验证**: 验证任务在时隙边界的状态保存和恢复，确保processing_progress和remaining_complexity的连续性。
        > 4. **模式一致性验证**: 相同任务在本地模式和卫星模式下的逻辑处理结果应该一致（排除通信和竞争因素）。

  * **效果评估方案 (Impact Evaluation Plan):**

      * **对比实验设计 (Comparative Experiment Design):**
        > 设置三组对比实验，使用相同的任务负载（1441时隙完整仿真）：
        > - **A组（卫星边缘计算）**: 使用DPSQ调度算法的卫星分布式处理
        > - **B组（本地处理基准）**: 所有任务在本地理想条件下处理，无通信延迟和资源竞争
        > - **C组（随机分发对比）**: 随机选择可见卫星进行任务分发，对比距离最近算法的效果
        > 
        > 关键对比指标：任务完成率、平均端到端延迟、系统总能耗、卫星资源利用率分布。

      * **期望结果与分析 (Expected Outcome & Analysis):**
        > **预期结果**: B组（本地处理）应展现最低延迟但无分布式优势；A组在保持合理完成率的同时体现分布式处理能力；C组性能最差，证明距离最近算法的优越性。
        > 
        > **科研价值**: 实验结果将量化卫星边缘计算相对于传统本地处理的性能权衡，为强化学习算法提供性能优化的基准目标，并验证智能任务分发策略在资源受限环境下的有效性。这将为后续研究动态自适应调度策略提供实证基础。

-----

### **使用建议**

本规划基于SPACE2项目现有90%完成度的架构，采用渐进式集成策略：
1. **阶段一**: 重构和标准化现有组件 (1-2天)
2. **阶段二**: 实现本地对比仿真环境 (2-3天)  
3. **阶段三**: 构建综合性能分析系统 (2天)
4. **阶段四**: 集成测试和性能优化 (1-2天)

总预计实施周期：6-8天，风险评级：低（基于成熟组件集成）