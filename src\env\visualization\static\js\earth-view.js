/**
 * SPACE2 3D Earth View
 * 实现3D地球视图和卫星可视化
 */

class EarthView {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        this.earth = null;
        
        // 节点存储
        this.satellites = new Map();
        this.satelliteLabels = new Map();
        this.groundStations = new Map();
        this.cloudCenters = new Map();
        this.connectionLines = new Map();
        
        // 状态管理
        this.selectedSatellite = null;
        this.isRotating = true;
        this.currentTimeslot = 0;
        
        // 事件回调
        this.onSatelliteClick = null;
        this.onSatelliteHover = null;
        
        // 常量
        this.EARTH_RADIUS = 6371; // km
        this.SATELLITE_ALTITUDE = 1200; // km
        this.SCALE_FACTOR = 0.01; // 缩放因子，便于显示
        
        this.init();
    }
    
    init() {
        this.createScene();
        this.createCamera();
        this.createRenderer();
        this.createControls();
        this.createEarth();
        this.createLights();
        this.setupEventListeners();
        this.animate();
        
        console.log('EarthView initialized');
    }
    
    createScene() {
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x000511);
        
        // 添加星空背景
        this.createStarField();
    }
    
    createStarField() {
        const starGeometry = new THREE.BufferGeometry();
        const starCount = 1000;
        const positions = new Float32Array(starCount * 3);
        
        for (let i = 0; i < starCount * 3; i += 3) {
            // 生成球面上的随机点
            const radius = 500;
            const theta = Math.random() * Math.PI * 2;
            const phi = Math.acos(2 * Math.random() - 1);
            
            positions[i] = radius * Math.sin(phi) * Math.cos(theta);
            positions[i + 1] = radius * Math.sin(phi) * Math.sin(theta);
            positions[i + 2] = radius * Math.cos(phi);
        }
        
        starGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        
        const starMaterial = new THREE.PointsMaterial({
            color: 0xffffff,
            size: 0.5,
            transparent: true,
            opacity: 0.8
        });
        
        const stars = new THREE.Points(starGeometry, starMaterial);
        this.scene.add(stars);
    }
    
    createCamera() {
        const aspect = this.container.clientWidth / this.container.clientHeight;
        this.camera = new THREE.PerspectiveCamera(60, aspect, 0.1, 1000);
        this.camera.position.set(0, 0, 150);
    }
    
    createRenderer() {
        this.renderer = new THREE.WebGLRenderer({ 
            antialias: true,
            alpha: true
        });
        this.renderer.setSize(this.container.clientWidth, this.container.clientHeight);
        this.renderer.setPixelRatio(window.devicePixelRatio);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        
        this.container.appendChild(this.renderer.domElement);
    }
    
    createControls() {
        this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;
        this.controls.enableZoom = true;
        this.controls.enablePan = true;
        this.controls.minDistance = 80;
        this.controls.maxDistance = 300;
        this.controls.autoRotate = true;
        this.controls.autoRotateSpeed = 0.5;
    }
    
    createEarth() {
        const earthGeometry = new THREE.SphereGeometry(this.EARTH_RADIUS * this.SCALE_FACTOR, 64, 64);
        
        // 创建地球材质
        const earthMaterial = new THREE.MeshPhongMaterial({
            color: 0x2194ce,
            shininess: 0.1
        });
        
        // 如果有地球纹理，可以加载
        // const textureLoader = new THREE.TextureLoader();
        // earthMaterial.map = textureLoader.load('textures/earth.jpg');
        
        this.earth = new THREE.Mesh(earthGeometry, earthMaterial);
        this.earth.castShadow = true;
        this.earth.receiveShadow = true;
        this.scene.add(this.earth);
        
        // 添加大气层效果
        this.createAtmosphere();
    }
    
    createAtmosphere() {
        const atmosphereGeometry = new THREE.SphereGeometry(
            this.EARTH_RADIUS * this.SCALE_FACTOR * 1.02, 32, 32
        );
        const atmosphereMaterial = new THREE.MeshBasicMaterial({
            color: 0x4fc3f7,
            transparent: true,
            opacity: 0.1,
            side: THREE.BackSide
        });
        
        const atmosphere = new THREE.Mesh(atmosphereGeometry, atmosphereMaterial);
        this.scene.add(atmosphere);
    }
    
    createLights() {
        // 主光源（模拟太阳）
        const sunLight = new THREE.DirectionalLight(0xffffff, 1);
        sunLight.position.set(5, 3, 5);
        sunLight.castShadow = true;
        sunLight.shadow.mapSize.width = 2048;
        sunLight.shadow.mapSize.height = 2048;
        this.scene.add(sunLight);
        
        // 环境光
        const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
        this.scene.add(ambientLight);
    }
    
    setupEventListeners() {
        // 窗口大小调整
        window.addEventListener('resize', () => this.onWindowResize());
        
        // 鼠标事件
        this.renderer.domElement.addEventListener('click', (event) => this.onMouseClick(event));
        this.renderer.domElement.addEventListener('mousemove', (event) => this.onMouseMove(event));
        
        // 键盘事件
        window.addEventListener('keydown', (event) => this.onKeyDown(event));
    }
    
    onWindowResize() {
        const width = this.container.clientWidth;
        const height = this.container.clientHeight;
        
        this.camera.aspect = width / height;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(width, height);
    }
    
    onMouseClick(event) {
        const intersectedObject = this.getIntersectedObject(event);
        
        if (intersectedObject && intersectedObject.userData.type === 'satellite') {
            const satelliteId = intersectedObject.userData.id;
            this.selectSatellite(satelliteId);
            
            if (this.onSatelliteClick) {
                this.onSatelliteClick(satelliteId, intersectedObject.userData.data);
            }
        } else if (!intersectedObject || intersectedObject.userData.type !== 'satellite') {
            // 点击空白处，取消选择
            this.clearSelection();
        }
    }
    
    onMouseMove(event) {
        const intersectedObject = this.getIntersectedObject(event);
        
        if (intersectedObject && intersectedObject.userData.type === 'satellite') {
            this.renderer.domElement.style.cursor = 'pointer';
            
            if (this.onSatelliteHover) {
                this.onSatelliteHover(intersectedObject.userData.id, intersectedObject.userData.data);
            }
        } else {
            this.renderer.domElement.style.cursor = 'default';
        }
    }
    
    onKeyDown(event) {
        switch(event.code) {
            case 'Space':
                event.preventDefault();
                this.toggleRotation();
                break;
            case 'KeyR':
                event.preventDefault();
                this.resetView();
                break;
            case 'KeyC':
                event.preventDefault();
                this.clearConnections();
                break;
        }
    }
    
    getIntersectedObject(event) {
        const rect = this.renderer.domElement.getBoundingClientRect();
        const mouse = new THREE.Vector2();
        mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
        
        const raycaster = new THREE.Raycaster();
        raycaster.setFromCamera(mouse, this.camera);
        
        // 只检测卫星对象
        const satelliteObjects = Array.from(this.satellites.values()).map(sat => sat.mesh);
        const intersects = raycaster.intersectObjects(satelliteObjects);
        
        return intersects.length > 0 ? intersects[0].object : null;
    }
    
    // 坐标转换方法
    latLonToVector3(lat, lon, radius) {
        const phi = (90 - lat) * (Math.PI / 180);
        const theta = (lon + 180) * (Math.PI / 180);
        
        const x = -(radius * Math.sin(phi) * Math.cos(theta));
        const z = (radius * Math.sin(phi) * Math.sin(theta));
        const y = (radius * Math.cos(phi));
        
        return new THREE.Vector3(x, y, z).multiplyScalar(this.SCALE_FACTOR);
    }
    
    // 更新所有卫星
    updateSatellites(satelliteData) {
        // 清除现有卫星
        this.clearSatellites();
        
        satelliteData.forEach(satData => {
            this.createSatellite(satData);
        });
        
        console.log(`Updated ${satelliteData.length} satellites`);
    }
    
    clearSatellites() {
        // 移除卫星网格
        this.satellites.forEach(satellite => {
            this.scene.remove(satellite.mesh);
            satellite.mesh.geometry.dispose();
            satellite.mesh.material.dispose();
        });
        this.satellites.clear();
        
        // 移除标签
        this.satelliteLabels.forEach(label => {
            this.scene.remove(label);
        });
        this.satelliteLabels.clear();
    }
    
    createSatellite(satData) {
        const position = this.latLonToVector3(
            satData.latitude, 
            satData.longitude, 
            this.EARTH_RADIUS + this.SATELLITE_ALTITUDE
        );
        
        // 根据卫星状态选择颜色和材质
        let color, opacity, size;
        if (!satData.state) {
            // 损坏卫星 - 红色
            color = 0xff4444;
            opacity = 0.8;
            size = 1.5;
        } else if (satData.illuminated) {
            // 正常光照卫星 - 绿色
            color = 0x00ff88;
            opacity = 0.9;
            size = 1.2;
        } else {
            // 阴影卫星 - 灰色
            color = 0x888888;
            opacity = 0.7;
            size = 1.0;
        }
        
        // 创建卫星几何体
        const geometry = new THREE.SphereGeometry(size, 16, 16);
        const material = new THREE.MeshPhongMaterial({
            color: color,
            transparent: true,
            opacity: opacity,
            emissive: color,
            emissiveIntensity: 0.2
        });
        
        const mesh = new THREE.Mesh(geometry, material);
        mesh.position.copy(position);
        
        // 添加用户数据
        mesh.userData = {
            type: 'satellite',
            id: satData.id,
            data: satData
        };
        
        // 创建文本标签
        const label = this.createTextLabel(satData.id, position, satData.state);
        
        // 存储卫星对象
        const satelliteObject = {
            mesh: mesh,
            label: label,
            data: satData,
            originalColor: color,
            originalOpacity: opacity
        };
        
        this.satellites.set(satData.id, satelliteObject);
        this.scene.add(mesh);
        
        if (label) {
            this.satelliteLabels.set(satData.id, label);
            this.scene.add(label);
        }
    }
    
    createTextLabel(text, position, isActive) {
        if (!isActive) {
            return null; // 损坏卫星不显示标签
        }
        
        // 创建画布
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        const fontSize = 48;
        context.font = `${fontSize}px Arial`;
        
        // 测量文本尺寸
        const textWidth = context.measureText(text).width;
        canvas.width = textWidth + 20;
        canvas.height = fontSize + 20;
        
        // 重新设置字体（canvas重置后需要重新设置）
        context.font = `${fontSize}px Arial`;
        context.fillStyle = 'rgba(0, 0, 0, 0.8)';
        context.fillRect(0, 0, canvas.width, canvas.height);
        
        context.fillStyle = 'white';
        context.textAlign = 'center';
        context.textBaseline = 'middle';
        context.fillText(text, canvas.width / 2, canvas.height / 2);
        
        // 创建纹理
        const texture = new THREE.CanvasTexture(canvas);
        const material = new THREE.SpriteMaterial({ 
            map: texture,
            transparent: true,
            opacity: 0.8
        });
        
        const sprite = new THREE.Sprite(material);
        sprite.position.copy(position);
        sprite.position.add(new THREE.Vector3(0, 3, 0)); // 标签在卫星上方
        sprite.scale.set(8, 4, 1);
        
        return sprite;
    }
    
    // 更新地面站
    updateGroundStations(groundStationData) {
        // 清除现有地面站
        this.groundStations.forEach(gs => {
            this.scene.remove(gs.mesh);
            gs.mesh.geometry.dispose();
            gs.mesh.material.dispose();
        });
        this.groundStations.clear();
        
        groundStationData.forEach(gsData => {
            this.createGroundStation(gsData);
        });
        
        console.log(`Updated ${groundStationData.length} ground stations`);
    }
    
    createGroundStation(gsData) {
        const position = this.latLonToVector3(
            gsData.latitude, 
            gsData.longitude, 
            this.EARTH_RADIUS + 2 // 稍微高于地面
        );
        
        // 创建三角形几何体（地面站用三角形表示）
        const geometry = new THREE.ConeGeometry(0.8, 2, 4);
        const material = new THREE.MeshPhongMaterial({
            color: 0xffff00,
            transparent: true,
            opacity: 0.8
        });
        
        const mesh = new THREE.Mesh(geometry, material);
        mesh.position.copy(position);
        
        // 让三角形指向地心
        mesh.lookAt(new THREE.Vector3(0, 0, 0));
        mesh.rotateX(Math.PI); // 翻转使尖端向上
        
        mesh.userData = {
            type: 'ground_station',
            id: gsData.id,
            data: gsData
        };
        
        this.groundStations.set(gsData.id, { mesh, data: gsData });
        this.scene.add(mesh);
    }
    
    // 更新云中心
    updateCloudCenters(cloudCenterData) {
        // 清除现有云中心
        this.cloudCenters.forEach(cc => {
            this.scene.remove(cc.mesh);
            cc.mesh.geometry.dispose();
            cc.mesh.material.dispose();
        });
        this.cloudCenters.clear();
        
        cloudCenterData.forEach(ccData => {
            this.createCloudCenter(ccData);
        });
        
        console.log(`Updated ${cloudCenterData.length} cloud centers`);
    }
    
    createCloudCenter(ccData) {
        const position = this.latLonToVector3(
            ccData.latitude, 
            ccData.longitude, 
            this.EARTH_RADIUS + 3 // 比地面站稍高
        );
        
        // 创建立方体几何体（云中心用立方体表示）
        const geometry = new THREE.BoxGeometry(2, 2, 2);
        const material = new THREE.MeshPhongMaterial({
            color: 0xff6600,
            transparent: true,
            opacity: 0.9
        });
        
        const mesh = new THREE.Mesh(geometry, material);
        mesh.position.copy(position);
        
        mesh.userData = {
            type: 'cloud_center',
            id: ccData.id,
            data: ccData
        };
        
        this.cloudCenters.set(ccData.id, { mesh, data: ccData });
        this.scene.add(mesh);
    }
    
    // 选择卫星
    selectSatellite(satelliteId) {
        // 清除之前的选择
        this.clearSelection();
        
        // 高亮选中的卫星
        const satellite = this.satellites.get(satelliteId);
        if (satellite && satellite.data.state) { // 只有正常卫星才能被选中
            this.selectedSatellite = satelliteId;
            
            // 改变卫星外观
            satellite.mesh.material.emissiveIntensity = 0.5;
            satellite.mesh.scale.setScalar(1.5);
            
            console.log(`Selected satellite: ${satelliteId}`);
        }
    }
    
    clearSelection() {
        if (this.selectedSatellite) {
            const satellite = this.satellites.get(this.selectedSatellite);
            if (satellite) {
                // 恢复原始外观
                satellite.mesh.material.emissiveIntensity = 0.2;
                satellite.mesh.scale.setScalar(1.0);
            }
            this.selectedSatellite = null;
        }
        
        // 清除连接线
        this.clearConnections();
    }
    
    // 清除连接线
    clearConnections() {
        this.connectionLines.forEach(line => {
            this.scene.remove(line);
            line.geometry.dispose();
            line.material.dispose();
        });
        this.connectionLines.clear();
    }
    
    // 控制方法
    toggleRotation() {
        this.isRotating = !this.isRotating;
        this.controls.autoRotate = this.isRotating;
    }
    
    resetView() {
        this.camera.position.set(0, 0, 150);
        this.controls.reset();
    }
    
    // 动画循环
    animate() {
        requestAnimationFrame(() => this.animate());
        
        // 更新控制器
        this.controls.update();
        
        // 更新动画
        this.updateAnimations();
        
        // 渲染场景
        this.renderer.render(this.scene, this.camera);
    }
    
    // 公共接口
    loadTimeslot(timeslot) {
        this.currentTimeslot = timeslot;
        this.clearSelection();
        
        // 这个方法会被 main.js 调用来加载新的时隙数据
        console.log(`Loading timeslot: ${timeslot}`);
    }
    
    getSelectedSatellite() {
        return this.selectedSatellite;
    }
    
    // 显示卫星连接
    showSatelliteConnections(satelliteId, connectionData) {
        // 清除现有连接
        this.clearConnections();
        
        const sourceSatellite = this.satellites.get(satelliteId);
        if (!sourceSatellite || !sourceSatellite.data.state) {
            console.log('Source satellite not found or damaged');
            return;
        }
        
        // 显示卫星间连接
        this.drawSatelliteConnections(satelliteId, connectionData.satellite_connections || []);
        
        // 显示地面站连接
        this.drawGroundConnections(satelliteId, connectionData.ground_connections || []);
        
        // 显示云中心连接
        this.drawCloudConnections(satelliteId, connectionData.cloud_connections || []);
        
        console.log(`Showed connections for satellite ${satelliteId}`);
    }
    
    drawSatelliteConnections(sourceId, connections) {
        const sourceSat = this.satellites.get(sourceId);
        if (!sourceSat) return;
        
        connections.forEach((conn, index) => {
            const targetSat = this.satellites.get(conn.target_id);
            if (!targetSat || !targetSat.data.state) return; // 跳过损坏的卫星
            
            const line = this.createConnectionLine(
                sourceSat.mesh.position,
                targetSat.mesh.position,
                {
                    color: 0x00ffff,
                    opacity: 0.8,
                    linewidth: this.calculateLineWidth(conn.data_rate_gbps, 'satellite'),
                    animated: conn.data_rate_gbps > 10 // 高速连接添加动画
                },
                'satellite',
                conn
            );
            
            this.connectionLines.set(`sat_${sourceId}_${conn.target_id}`, line);
            this.scene.add(line);
        });
    }
    
    drawGroundConnections(sourceId, connections) {
        const sourceSat = this.satellites.get(sourceId);
        if (!sourceSat) return;
        
        connections.forEach((conn, index) => {
            const targetGs = this.groundStations.get(conn.target_id);
            if (!targetGs) return;
            
            const line = this.createConnectionLine(
                sourceSat.mesh.position,
                targetGs.mesh.position,
                {
                    color: 0xffff00,
                    opacity: 0.7,
                    linewidth: this.calculateLineWidth(conn.uplink_rate_mbps / 1000, 'ground'),
                    dashed: true
                },
                'ground',
                conn
            );
            
            this.connectionLines.set(`ground_${sourceId}_${conn.target_id}`, line);
            this.scene.add(line);
        });
    }
    
    drawCloudConnections(sourceId, connections) {
        const sourceSat = this.satellites.get(sourceId);
        if (!sourceSat) return;
        
        connections.forEach((conn, index) => {
            const targetCc = this.cloudCenters.get(conn.target_id);
            if (!targetCc) return;
            
            const line = this.createConnectionLine(
                sourceSat.mesh.position,
                targetCc.mesh.position,
                {
                    color: 0xff6600,
                    opacity: 0.9,
                    linewidth: this.calculateLineWidth(conn.uplink_rate_mbps / 1000, 'cloud'),
                    thick: true
                },
                'cloud',
                conn
            );
            
            this.connectionLines.set(`cloud_${sourceId}_${conn.target_id}`, line);
            this.scene.add(line);
        });
    }
    
    createConnectionLine(startPos, endPos, style, connectionType, connectionData) {
        const geometry = new THREE.BufferGeometry().setFromPoints([startPos, endPos]);
        
        let material;
        
        if (style.dashed) {
            // 虚线材质（地面站连接）
            material = new THREE.LineDashedMaterial({
                color: style.color,
                transparent: true,
                opacity: style.opacity,
                dashSize: 2,
                gapSize: 1,
                linewidth: style.linewidth || 1
            });
        } else {
            // 实线材质
            material = new THREE.LineBasicMaterial({
                color: style.color,
                transparent: true,
                opacity: style.opacity,
                linewidth: style.linewidth || 1
            });
        }
        
        const line = new THREE.Line(geometry, material);
        
        // 如果是虚线，需要计算线段
        if (style.dashed) {
            line.computeLineDistances();
        }
        
        // 添加用户数据
        line.userData = {
            type: 'connection',
            connectionType: connectionType,
            data: connectionData,
            sourcePos: startPos.clone(),
            targetPos: endPos.clone()
        };
        
        // 添加动画效果（高速连接）
        if (style.animated) {
            this.addDataFlowAnimation(line);
        }
        
        return line;
    }
    
    calculateLineWidth(dataRateGbps, connectionType) {
        // 根据数据速率计算线条粗细
        const baseWidth = {
            'satellite': 1,
            'ground': 0.5,
            'cloud': 1.5
        }[connectionType] || 1;
        
        const maxWidth = {
            'satellite': 4,
            'ground': 2,
            'cloud': 5
        }[connectionType] || 3;
        
        // 对数缩放
        const normalizedRate = Math.min(Math.log10(dataRateGbps + 1) / 2, 1);
        return baseWidth + (maxWidth - baseWidth) * normalizedRate;
    }
    
    addDataFlowAnimation(line) {
        // 创建数据流动效果的粒子
        const particleCount = 5;
        const geometry = new THREE.BufferGeometry();
        const positions = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);
        
        for (let i = 0; i < particleCount; i++) {
            const t = i / particleCount;
            const pos = line.userData.sourcePos.clone().lerp(line.userData.targetPos, t);
            
            positions[i * 3] = pos.x;
            positions[i * 3 + 1] = pos.y;
            positions[i * 3 + 2] = pos.z;
            
            colors[i * 3] = 1; // R
            colors[i * 3 + 1] = 1; // G
            colors[i * 3 + 2] = 1; // B
        }
        
        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        
        const material = new THREE.PointsMaterial({
            size: 0.5,
            vertexColors: true,
            transparent: true,
            opacity: 0.8
        });
        
        const particles = new THREE.Points(geometry, material);
        particles.userData.animationData = {
            line: line,
            time: 0,
            speed: 0.02
        };
        
        this.scene.add(particles);
        
        // 添加到连接线管理中
        const lineKey = Object.keys(this.connectionLines).find(key => 
            this.connectionLines[key] === line
        );
        if (lineKey) {
            this.connectionLines.set(lineKey + '_particles', particles);
        }
    }
    
    // 在动画循环中更新粒子动画
    updateAnimations() {
        this.connectionLines.forEach(object => {
            if (object.userData.animationData) {
                const animData = object.userData.animationData;
                animData.time += animData.speed;
                
                const positions = object.geometry.attributes.position.array;
                const sourcePos = animData.line.userData.sourcePos;
                const targetPos = animData.line.userData.targetPos;
                
                for (let i = 0; i < positions.length / 3; i++) {
                    const t = (i / (positions.length / 3) + animData.time) % 1;
                    const pos = sourcePos.clone().lerp(targetPos, t);
                    
                    positions[i * 3] = pos.x;
                    positions[i * 3 + 1] = pos.y;
                    positions[i * 3 + 2] = pos.z;
                }
                
                object.geometry.attributes.position.needsUpdate = true;
            }
        });
    }
    
    // 销毁方法
    dispose() {
        // 清理所有3D对象
        this.clearSatellites();
        this.clearConnections();
        
        // 清理渲染器
        if (this.renderer) {
            this.renderer.dispose();
        }
        
        // 移除事件监听器
        window.removeEventListener('resize', this.onWindowResize);
    }
}