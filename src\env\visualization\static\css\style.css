/* SPACE2 Visualization Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', 'Microsoft YaHei', Tahoma, Geneva, Verdana, sans-serif;
    background: #1a1a1a;
    color: #ffffff;
    overflow: hidden;
    font-size: 14px;
}

/* Header Styles */
.header {
    height: 70px;
    background: linear-gradient(90deg, #2d2d2d, #3d3d3d);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.5);
    border-bottom: 2px solid #0066cc;
}

.title h2 {
    color: #66ccff;
    font-size: 20px;
    font-weight: 600;
    margin: 0;
}

.controls {
    display: flex;
    gap: 30px;
    align-items: center;
}

.timeslot-control {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 15px;
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
    border: 1px solid #555;
}

.timeslot-control label {
    font-weight: 500;
    color: #ccc;
}

.timeslot-input {
    width: 80px;
    padding: 6px 10px;
    background: #3d3d3d;
    border: 1px solid #555;
    color: white;
    border-radius: 4px;
    font-size: 14px;
}

.timeslot-input:focus {
    outline: none;
    border-color: #66ccff;
    box-shadow: 0 0 5px rgba(102, 204, 255, 0.3);
}

.selected-satellite {
    background: linear-gradient(90deg, #0066cc, #0088ff);
    padding: 8px 20px;
    border-radius: 20px;
    font-weight: bold;
    color: white;
    min-width: 150px;
    text-align: center;
    border: 2px solid #66ccff;
}

.system-stats {
    display: flex;
    flex-direction: column;
    gap: 4px;
    font-size: 12px;
    color: #ccc;
}

.system-stats span {
    padding: 2px 8px;
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
}

/* Button Styles */
.btn {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-primary {
    background: #0066cc;
    color: white;
}

.btn-primary:hover {
    background: #0088ff;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #555;
    color: white;
}

.btn-secondary:hover {
    background: #666;
}

/* Main Container */
.main-container {
    display: flex;
    height: calc(100vh - 70px);
}

/* Earth View */
.earth-view {
    flex: 1;
    position: relative;
    background: radial-gradient(circle at center, #001122, #000000);
    overflow: hidden;
}

.legend {
    position: absolute;
    top: 20px;
    left: 20px;
    background: rgba(45, 45, 45, 0.95);
    padding: 15px;
    border-radius: 10px;
    font-size: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.5);
    border: 1px solid #555;
    min-width: 180px;
    z-index: 100;
}

.legend-title {
    font-weight: bold;
    color: #66ccff;
    margin-bottom: 10px;
    font-size: 13px;
    border-bottom: 1px solid #555;
    padding-bottom: 5px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 6px;
    padding: 2px 0;
}

.legend-line {
    width: 25px;
    height: 2px;
    border-radius: 1px;
}

.satellite-line {
    background: #00ffff;
}

.ground-line {
    background: #ffff00;
    border-style: dashed;
    height: 1px;
    border-top: 2px dashed #ffff00;
    background: none;
}

.cloud-line {
    background: #ff6600;
    height: 3px;
}

.legend-dot {
    font-size: 16px;
    width: 25px;
    text-align: center;
}

.normal-satellite {
    color: #00ff88;
}

.shadow-satellite {
    color: #888888;
}

.damaged-satellite {
    color: #ff4444;
}

.ground-station {
    color: #ffff00;
}

.cloud-center {
    color: #ff6600;
}

.view-controls {
    position: absolute;
    top: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    z-index: 100;
}

.loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    background: rgba(0,0,0,0.8);
    padding: 30px;
    border-radius: 10px;
    border: 1px solid #555;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #333;
    border-top: 4px solid #66ccff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error-message {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 0, 0, 0.1);
    border: 2px solid #ff4444;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    max-width: 400px;
}

.error-content h4 {
    color: #ff4444;
    margin-bottom: 10px;
}

/* Details Panel */
.details-panel {
    width: 380px;
    background: linear-gradient(180deg, #2d2d2d, #252525);
    border-left: 2px solid #0066cc;
    padding: 20px;
    overflow-y: auto;
    box-shadow: -5px 0 15px rgba(0,0,0,0.3);
}

.details-panel::-webkit-scrollbar {
    width: 8px;
}

.details-panel::-webkit-scrollbar-track {
    background: #3d3d3d;
    border-radius: 4px;
}

.details-panel::-webkit-scrollbar-thumb {
    background: #666;
    border-radius: 4px;
}

.details-panel::-webkit-scrollbar-thumb:hover {
    background: #888;
}

.satellite-info {
    margin-bottom: 25px;
}

.info-section {
    margin-bottom: 20px;
    padding: 18px;
    background: linear-gradient(145deg, #3d3d3d, #353535);
    border-radius: 10px;
    border: 1px solid #555;
    box-shadow: 0 4px 10px rgba(0,0,0,0.3);
}

.info-title {
    font-size: 15px;
    font-weight: bold;
    margin-bottom: 12px;
    color: #66ccff;
    border-bottom: 1px solid #555;
    padding-bottom: 6px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.info-title::before {
    content: "●";
    color: #66ccff;
    font-size: 12px;
}

.info-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    color: #ccc;
    font-size: 13px;
}

.stat-value {
    color: #fff;
    font-weight: 500;
    font-size: 13px;
}

.task-queue {
    max-height: 300px;
    overflow-y: auto;
}

.task-queue::-webkit-scrollbar {
    width: 6px;
}

.task-queue::-webkit-scrollbar-track {
    background: #4d4d4d;
    border-radius: 3px;
}

.task-queue::-webkit-scrollbar-thumb {
    background: #666;
    border-radius: 3px;
}

.no-selection {
    text-align: center;
    color: #888;
    padding: 30px 10px;
    font-style: italic;
}

.task-item {
    display: flex;
    flex-direction: column;
    gap: 6px;
    padding: 12px;
    margin-bottom: 10px;
    background: linear-gradient(145deg, #4d4d4d, #424242);
    border-radius: 8px;
    border-left: 4px solid #66ccff;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    transition: transform 0.2s ease;
}

.task-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 10px rgba(0,0,0,0.3);
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.task-id {
    font-weight: bold;
    color: #66ccff;
    font-size: 12px;
}

.task-status {
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 500;
}

.task-status.processing {
    background: #28a745;
    color: white;
}

.task-status.queued {
    background: #ffc107;
    color: black;
}

.task-status.completed {
    background: #17a2b8;
    color: white;
}

.task-progress {
    width: 100%;
    height: 8px;
    background: #555;
    border-radius: 4px;
    overflow: hidden;
    margin: 4px 0;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #00ff88, #66ccff);
    border-radius: 4px;
    transition: width 0.5s ease;
    position: relative;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.task-info {
    display: flex;
    justify-content: space-between;
    font-size: 11px;
    color: #ccc;
}

.task-priority {
    padding: 1px 6px;
    border-radius: 8px;
    font-size: 10px;
}

.task-priority.high {
    background: #dc3545;
    color: white;
}

.task-priority.medium {
    background: #fd7e14;
    color: white;
}

.task-priority.low {
    background: #6c757d;
    color: white;
}

/* Status indicators */
.status-normal {
    color: #00ff88;
}

.status-shadow {
    color: #888888;
}

.status-damaged {
    color: #ff4444;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .details-panel {
        width: 320px;
    }
    
    .controls {
        gap: 15px;
    }
    
    .selected-satellite {
        min-width: 120px;
        padding: 6px 15px;
    }
}

@media (max-width: 768px) {
    .header {
        flex-direction: column;
        height: auto;
        padding: 10px;
        gap: 10px;
    }
    
    .main-container {
        height: calc(100vh - 120px);
        flex-direction: column;
    }
    
    .details-panel {
        width: 100%;
        height: 300px;
        border-left: none;
        border-top: 2px solid #0066cc;
    }
    
    .legend {
        font-size: 11px;
        padding: 10px;
    }
}

/* Animation classes */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateX(20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* Tooltips */
.tooltip {
    position: absolute;
    background: rgba(0,0,0,0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    pointer-events: none;
    z-index: 1000;
    border: 1px solid #666;
    box-shadow: 0 4px 15px rgba(0,0,0,0.5);
}

.tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: rgba(0,0,0,0.9) transparent transparent transparent;
}