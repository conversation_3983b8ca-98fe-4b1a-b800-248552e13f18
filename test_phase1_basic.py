"""
Basic test for Phase 1 satellite cloud computing system
Tests core functionality without complex imports
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

from datetime import datetime
import yaml

def test_basic_imports():
    """Test basic module imports"""
    try:
        from src.env.satellite_cloud.compute_models import (
            ComputeTask, TaskStatus, ProcessingResult
        )
        from src.env.satellite_cloud.satellite_compute import SatelliteCompute
        from src.env.satellite_cloud.cloud_compute import CloudCompute
        from src.env.satellite_cloud.compute_manager import ComputeManager
        from src.env.physics_layer.task_models import Task
        
        print("PASS: All modules imported successfully")
        return True
    except Exception as e:
        print(f"FAIL: Module import failed: {e}")
        return False

def test_task_creation():
    """Test task creation and conversion"""
    try:
        from src.env.satellite_cloud.compute_models import ComputeTask, TaskStatus
        from src.env.physics_layer.task_models import Task
        
        # Create a test task
        task = Task(
            task_id=1,
            user_id=100,
            priority=3.0,
            data_size_mb=10.0,
            complexity_cycles_per_bit=1000,
            deadline_ms=5000,
            creation_timestamp=datetime.now()
        )
        
        # Convert to ComputeTask
        compute_task = ComputeTask.from_task(task, arrival_time=1000.0)
        
        print(f"PASS: Task creation successful:")
        print(f"  Task ID: {compute_task.task_id}")
        print(f"  Priority: {compute_task.priority}")
        print(f"  Data size: {compute_task.data_size_mb}MB")
        print(f"  Complexity: {compute_task.complexity}")
        print(f"  Status: {compute_task.status.value}")
        
        return True
    except Exception as e:
        print(f"FAIL: Task creation failed: {e}")
        return False

def test_satellite_compute():
    """Test satellite compute basic functionality"""
    try:
        from src.env.satellite_cloud.satellite_compute import SatelliteCompute
        from src.env.physics_layer.task_models import Task
        
        # Create configuration
        config = {
            'queuing': {
                'w_priority': 1.0,
                'w_urgency': 2.0,
                'w_cost': 0.5,
                'epsilon_urgency': 1.0e-6,
                'max_queue_size': 100
            },
            'computation': {
                'f_leo_hz': 500e9,
                'zeta_leo': 1.0e-10,
                'processing_overhead_ratio': 0.05,
                'leo_battery_capacity_j': 3600000,
                'leo_solar_power_w': 5000,
                'energy_threshold_ratio': 0.2,
                'default_drop_penalty': 100.0,
                'min_cpu_allocation': 10.0
            },
            'communication': {
                'b_us_hz': 1000e6,
                'mb_to_bits': 8e6
            }
        }
        
        # Create satellite
        satellite = SatelliteCompute(satellite_id=0, config=config)
        
        # Create and add task
        task = Task(
            task_id=1,
            user_id=100,
            priority=3.0,
            data_size_mb=5.0,
            complexity_cycles_per_bit=1000,
            deadline_ms=10000,
            creation_timestamp=datetime.now()
        )
        
        success = satellite.add_task(task)
        
        print(f"✓ 卫星计算节点创建成功:")
        print(f"  卫星ID: {satellite.satellite_id}")
        print(f"  CPU频率: {satellite.cpu_frequency/1e9:.1f}GHz")
        print(f"  最大电池: {satellite.max_battery/1e6:.1f}MJ")
        print(f"  任务添加: {success}")
        print(f"  队列长度: {len(satellite.task_queue)}")
        
        return True
    except Exception as e:
        print(f"✗ 卫星计算节点测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cloud_compute():
    """Test cloud compute basic functionality"""
    try:
        from src.env.satellite_cloud.cloud_compute import CloudCompute
        from src.env.physics_layer.task_models import Task
        
        # Create configuration
        config = {
            'computation': {
                'f_cloud_hz': 100e9,
                'max_parallel_tasks': 200,
                'default_drop_penalty': 100.0
            },
            'queuing': {
                'max_queue_size': 1000
            }
        }
        
        # Create cloud
        cloud = CloudCompute(cloud_id=0, config=config)
        
        # Create and add task
        task = Task(
            task_id=2,
            user_id=101,
            priority=2.0,
            data_size_mb=8.0,
            complexity_cycles_per_bit=1500,
            deadline_ms=15000,
            creation_timestamp=datetime.now()
        )
        
        success = cloud.add_task(task, arrival_time=1000.0)
        
        print(f"✓ 云计算中心创建成功:")
        print(f"  云中心ID: {cloud.cloud_id}")
        print(f"  CPU频率: {cloud.cpu_frequency/1e9:.1f}GHz")
        print(f"  最大并行任务: {cloud.max_parallel_tasks}")
        print(f"  任务添加: {success}")
        print(f"  队列长度: {len(cloud.task_queue)}")
        
        return True
    except Exception as e:
        print(f"✗ 云计算中心测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_compute_manager():
    """Test compute manager basic functionality"""
    try:
        from src.env.satellite_cloud.compute_manager import ComputeManager
        from src.env.physics_layer.task_models import Task
        
        # Create configuration
        config = {
            'system': {
                'num_leo_satellites': 2,
                'num_cloud_centers': 1
            },
            'queuing': {
                'w_priority': 1.0,
                'w_urgency': 2.0,
                'w_cost': 0.5,
                'epsilon_urgency': 1.0e-6,
                'max_queue_size': 50
            },
            'computation': {
                'f_leo_hz': 500e9,
                'f_cloud_hz': 100e9,
                'zeta_leo': 1.0e-10,
                'processing_overhead_ratio': 0.05,
                'leo_battery_capacity_j': 3600000,
                'leo_solar_power_w': 5000,
                'energy_threshold_ratio': 0.2,
                'default_drop_penalty': 100.0,
                'min_cpu_allocation': 10.0,
                'max_parallel_tasks': 100
            },
            'communication': {
                'b_us_hz': 1000e6,
                'mb_to_bits': 8e6
            },
            'offloading': {
                'strategy': 'deadline_aware',
                'max_offload_ratio': 0.7,
                'energy_threshold': 0.3,
                'queue_threshold': 0.8
            }
        }
        
        # Create manager
        manager = ComputeManager(config=config)
        
        # Create and distribute task
        task = Task(
            task_id=3,
            user_id=102,
            priority=4.0,
            data_size_mb=12.0,
            complexity_cycles_per_bit=2000,
            deadline_ms=8000,
            creation_timestamp=datetime.now()
        )
        
        illuminated_status = {0: True, 1: True}
        success = manager.distribute_task(task, 0, 1000.0, illuminated_status)
        
        print(f"✓ 计算管理器创建成功:")
        print(f"  卫星数量: {len(manager.satellites)}")
        print(f"  云中心数量: {len(manager.clouds)}")
        print(f"  卸载策略: {manager.strategy}")
        print(f"  任务分发: {success}")
        print(f"  总接收任务: {manager.total_tasks_received}")
        
        return True
    except Exception as e:
        print(f"✗ 计算管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run basic Phase 1 tests"""
    print("SPACE2 卫星云计算系统 Phase 1 基础测试")
    print("=" * 60)
    
    tests = [
        ("模块导入", test_basic_imports),
        ("任务创建", test_task_creation),
        ("卫星计算", test_satellite_compute),
        ("云计算", test_cloud_compute),
        ("计算管理器", test_compute_manager)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        if test_func():
            passed += 1
        else:
            print(f"测试失败: {test_name}")
    
    print(f"\n" + "=" * 60)
    print(f"测试总结: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 Phase 1 基础功能测试全部通过!")
        print("\nPhase 1 实现的功能:")
        print("- ✓ 计算模型定义 (ComputeTask, ProcessingResult, etc.)")
        print("- ✓ 卫星计算节点 (DPSQ调度算法, 能量管理)")
        print("- ✓ 云计算中心 (并行处理, 无能量约束)")
        print("- ✓ 统一计算管理器 (智能卸载决策)")
        print("- ✓ 多种卸载策略 (截止时间感知, 能量感知, 负载均衡)")
        print("\n可以继续进行 Phase 2 开发!")
    else:
        print(f"❌ 存在 {total - passed} 个失败的测试，需要修复后再继续")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)