"""
Satellite compute resource management module
Implements DPSQ (Dynamic Priority Score Queuing) scheduling algorithm
Focuses on compute resource management without task_tracking dependency
"""

import numpy as np
from typing import List, Dict, Optional, Tuple, Any
import heapq
import logging
import yaml
from pathlib import Path

# Absolute imports as per coding standard
from src.env.satellite_cloud.compute_models import (
    ComputeTask, TaskStatus, ProcessingNode, NodeType, SatelliteState, ProcessingResult
)
from src.env.physics_layer.task_models import Task
from src.env.physics_layer.orbital import OrbitalUpdater
from src.env.physics_layer.communication_refactored import CommunicationManager
from src.env.Foundation_Layer.error_handling import handle_errors, SpaceSimulationError
from src.env.Foundation_Layer.logging_config import get_logger


class DPSQScheduler:
    """Dynamic Priority Score Queuing scheduler"""
    
    def __init__(self, config: Dict):
        """
        Initialize scheduler with configuration parameters
        
        Args:
            config: Configuration dictionary from config.yaml
        """
        # Extract queuing parameters - no default values as per coding standard
        queuing_config = config['queuing']
        self.w_priority = queuing_config['w_priority']
        self.w_urgency = queuing_config['w_urgency']
        self.w_cost = queuing_config['w_cost']
        self.epsilon_urgency = queuing_config['epsilon_urgency']
        self.max_queue_size = queuing_config['max_queue_size']
        
        # Extract computation parameters - no default values as per coding standard
        computation_config = config['computation']
        self.f_sat = float(computation_config['f_leo_hz'])
        self.zeta_leo = float(computation_config['zeta_leo'])
        self.processing_overhead = computation_config['processing_overhead_ratio']
        
        # Extract communication parameters - no default values as per coding standard
        comm_config = config['communication']
        self.default_bandwidth = comm_config['b_us_hz'] / 8e6  # Convert to MB/s
        
        self.logger = get_logger(__name__)
        
    def calculate_priority_score(self, task: ComputeTask, current_time: float, 
                                bandwidth: Optional[float] = None) -> float:
        """
        Calculate dynamic priority score for task
        
        Score(T_i, t_now) = w_p * f_p(P_i) + w_d * f_d(D_i, t_now) - w_c * f_c(S_i, C_i)
        
        Args:
            task: Task object
            current_time: Current time (seconds)
            bandwidth: Available bandwidth (MB/s)
            
        Returns:
            Dynamic priority score
        """
        if bandwidth is None or bandwidth <= 0:
            bandwidth = self.default_bandwidth
            
        # Priority factor: f_p(P_i) = P_i
        f_priority = task.priority
        
        # Urgency factor: f_d(D_i, t_now) = 1 / ((D_i - t_now) + epsilon)
        time_remaining = max(task.deadline - current_time, 0)
        f_urgency = 1.0 / (time_remaining + self.epsilon_urgency)
        
        # Cost factor: f_c(S_i, C_i) = T_proc,i = S_i/B_link + C_i/F_sat
        if bandwidth > 0:
            communication_time = task.data_size_mb / bandwidth
        else:
            communication_time = float('inf')
        computation_time = task.complexity / self.f_sat
        f_cost = communication_time + computation_time
        
        # Calculate total score
        score = (self.w_priority * f_priority + 
                self.w_urgency * f_urgency - 
                self.w_cost * f_cost)
        
        return score
    
    def estimate_processing_time(self, task: ComputeTask, 
                                cpu_allocation: float = 1.0,
                                bandwidth: Optional[float] = None) -> Tuple[float, float, float]:
        """
        Estimate task processing time
        
        Args:
            task: Task object
            cpu_allocation: CPU allocation ratio (0-1)
            bandwidth: Available bandwidth (MB/s)
            
        Returns:
            (total_time, communication_time, computation_time) in seconds
        """
        if bandwidth is None or bandwidth <= 0:
            bandwidth = self.default_bandwidth
            
        # Communication delay
        if bandwidth > 0:
            communication_time = task.data_size_mb / bandwidth
        else:
            communication_time = float('inf')
        
        # Computation delay (considering CPU allocation)
        effective_cpu = self.f_sat * cpu_allocation
        computation_time = task.complexity / effective_cpu
        
        # Consider processing overhead
        total_time = (communication_time + computation_time) * (1 + self.processing_overhead)
        
        return total_time, communication_time, computation_time
    
    def check_feasibility(self, task: ComputeTask, current_time: float,
                         cpu_allocation: float = 1.0,
                         bandwidth: Optional[float] = None) -> bool:
        """
        Check if task can be completed before deadline
        
        Args:
            task: Task object
            current_time: Current time
            cpu_allocation: CPU allocation ratio
            bandwidth: Available bandwidth
            
        Returns:
            True if feasible, False otherwise
        """
        total_time, _, _ = self.estimate_processing_time(task, cpu_allocation, bandwidth)
        estimated_completion = current_time + total_time
        
        return estimated_completion <= task.deadline


class SatelliteCompute:
    """Satellite compute resource management class"""
    
    def __init__(self, satellite_id: int, config_path: str = None, 
                 config: Dict = None,
                 orbital_updater: Optional[OrbitalUpdater] = None,
                 comm_manager: Optional[CommunicationManager] = None):
        """
        Initialize satellite compute manager
        
        Args:
            satellite_id: Satellite ID
            config_path: Path to configuration file
            config: Configuration dictionary (if config_path not provided)
            orbital_updater: Orbital updater instance
            comm_manager: Communication manager instance
        """
        self.satellite_id = satellite_id
        
        # Load configuration - must be provided via parameter as per coding standard
        if config is not None:
            self.config = config
        elif config_path is not None:
            with open(config_path, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)
        else:
            raise ValueError("Either config_path or config must be provided")
        
        # Initialize external managers
        self.orbital_updater = orbital_updater
        self.comm_manager = comm_manager
        
        # Initialize scheduler
        self.scheduler = DPSQScheduler(self.config)
        
        # Task queues and state
        self.task_queue: List[ComputeTask] = []
        self.current_task: Optional[ComputeTask] = None  # Only one task at a time (atomic)
        self.completed_tasks: List[ComputeTask] = []
        self.dropped_tasks: List[ComputeTask] = []
        
        # Resource state - no default values as per coding standard
        computation_config = self.config['computation']
        self.cpu_frequency = float(computation_config['f_leo_hz'])
        self.max_battery = float(computation_config['leo_battery_capacity_j'])
        self.battery_energy = self.max_battery
        self.solar_power = float(computation_config['leo_solar_power_w'])
        self.energy_threshold = computation_config['energy_threshold_ratio']
        
        # Processing state
        self.is_processing = False
        self.processing_progress = 0.0
        self.processing_start_time = 0.0
        
        # Communication state - no default values as per coding standard
        comm_config = self.config['communication']
        self.current_bandwidth = comm_config['b_us_hz'] / 8e6  # MB/s
        
        # Performance tracking
        self.total_energy_consumed = 0.0
        self.total_tasks_processed = 0
        self.total_tasks_dropped = 0
        self.total_penalty = 0.0
        
        # Caching for performance
        self.priority_cache: Dict[str, float] = {}
        
        self.logger = get_logger(f"SatelliteCompute_{satellite_id}")
        
    @handle_errors(module="satellite_compute", function="add_task")
    def add_task(self, task: Task) -> bool:
        """
        Add task to processing queue
        
        Args:
            task: Task object from task_models
            
        Returns:
            True if successfully added, False if rejected
        """
        # Check queue capacity
        if len(self.task_queue) >= self.scheduler.max_queue_size:
            self.logger.warning(f"Queue full, rejecting task {task.task_id}")
            return False
        
        # Convert Task to ComputeTask
        compute_task = ComputeTask.from_task(
            task, 
            arrival_time=0.0,  # Will be set by caller
            drop_penalty=self.config['computation']['default_drop_penalty']
        )
        
        # Check energy feasibility
        if not self.can_accept_task(compute_task):
            self.logger.warning(f"Insufficient energy for task {task.task_id}")
            return False
        
        self.task_queue.append(compute_task)
        self.logger.info(f"Added task {task.task_id} to queue (queue size: {len(self.task_queue)})")
        return True
    
    def can_accept_task(self, task: ComputeTask) -> bool:
        """
        Check if satellite can accept task (energy constraint)
        
        Args:
            task: ComputeTask object
            
        Returns:
            True if can accept, False otherwise
        """
        # Calculate required energy
        required_energy = self.scheduler.zeta_leo * task.complexity
        
        # Check against energy threshold
        min_energy = self.max_battery * self.energy_threshold
        available_energy = self.battery_energy - min_energy
        
        return available_energy >= required_energy
    
    @handle_errors(module="satellite_compute", function="process_timeslot")
    def process_timeslot(self, duration: float, illuminated: bool) -> ProcessingResult:
        """
        Process tasks for one timeslot
        
        Args:
            duration: Timeslot duration in seconds
            illuminated: Whether satellite is in sunlight
            
        Returns:
            ProcessingResult with completed tasks and metrics
        """
        result = ProcessingResult()
        
        # Update energy state
        self._update_energy(duration, illuminated)
        
        # Process current task if any
        if self.current_task is not None:
            completed_task = self._process_current_task(duration)
            if completed_task:
                result.completed_tasks.append(completed_task)
                self.completed_tasks.append(completed_task)
                self.total_tasks_processed += 1
                self.current_task = None
                self.is_processing = False
                self.processing_progress = 0.0
        
        # Schedule new task if no current task
        if self.current_task is None and self.task_queue:
            scheduled_task = self._schedule_next_task()
            if scheduled_task:
                self.current_task = scheduled_task
                self.is_processing = True
                self.processing_start_time = 0.0  # Will be set by caller
                self.processing_progress = 0.0
        
        # Check timeouts and drop expired tasks
        self._check_timeouts()
        
        # Calculate metrics
        result.energy_consumed = self.total_energy_consumed
        result.cpu_utilization = 1.0 if self.is_processing else 0.0
        result.queue_wait_time = self._calculate_average_wait_time()
        
        return result
    
    def _update_energy(self, duration: float, illuminated: bool):
        """Update battery energy state"""
        # Solar charging if illuminated
        if illuminated:
            charged_energy = self.solar_power * duration
            self.battery_energy = min(self.battery_energy + charged_energy, self.max_battery)
        
        # Energy consumption from processing
        if self.is_processing and self.current_task:
            # Calculate energy consumed based on actual processing
            cycles_per_second = self.cpu_frequency
            energy_per_second = cycles_per_second * self.scheduler.zeta_leo
            consumed_energy = energy_per_second * duration
            
            self.battery_energy = max(0, self.battery_energy - consumed_energy)
            self.total_energy_consumed += consumed_energy
            
            if self.current_task:
                self.current_task.accumulated_energy += consumed_energy
    
    def _process_current_task(self, duration: float) -> Optional[ComputeTask]:
        """
        Process current task for given duration
        
        Args:
            duration: Processing duration in seconds
            
        Returns:
            Completed task if finished, None otherwise
        """
        if not self.current_task:
            return None
        
        # Calculate processing cycles for this duration
        cycles_available = self.cpu_frequency * duration
        cycles_needed = self.current_task.remaining_complexity
        
        # Process (limited by remaining complexity)
        cycles_processed = min(cycles_available, cycles_needed)
        self.current_task.remaining_complexity -= cycles_processed
        
        # Update progress
        if self.current_task.complexity > 0:
            self.processing_progress = 1.0 - (self.current_task.remaining_complexity / self.current_task.complexity)
        else:
            self.processing_progress = 1.0
        
        # Update task timing
        self.current_task.accumulated_processing_time += duration
        
        # Check if completed
        if self.current_task.remaining_complexity <= 0:
            self.current_task.status = TaskStatus.COMPLETED
            self.current_task.processing_progress = 1.0
            self.current_task.energy_consumed = self.current_task.accumulated_energy
            return self.current_task
        
        return None
    
    def _schedule_next_task(self) -> Optional[ComputeTask]:
        """
        Schedule next task using DPSQ algorithm
        
        Returns:
            Selected task or None if no suitable task
        """
        if not self.task_queue:
            return None
        
        # Calculate priority scores for all tasks
        current_time = 0.0  # Will be provided by environment
        task_scores = []
        
        for task in self.task_queue:
            score = self.scheduler.calculate_priority_score(
                task, current_time, self.current_bandwidth
            )
            
            # Check feasibility
            if self.scheduler.check_feasibility(task, current_time, 1.0, self.current_bandwidth):
                task_scores.append((score, task))
        
        if not task_scores:
            # No feasible tasks, drop the oldest expired task
            for task in self.task_queue[:]:
                if task.deadline <= current_time:
                    self._drop_task(task, "timeout")
            return None
        
        # Select task with highest score
        task_scores.sort(key=lambda x: x[0], reverse=True)
        _, selected_task = task_scores[0]
        
        # Remove from queue
        self.task_queue.remove(selected_task)
        
        # Initialize task for processing
        selected_task.status = TaskStatus.PROCESSING
        selected_task.start_time = current_time
        selected_task.allocated_cpu = 1.0  # Full CPU allocation (atomic processing)
        
        # Estimate completion time
        total_time, comm_time, comp_time = self.scheduler.estimate_processing_time(
            selected_task, 1.0, self.current_bandwidth
        )
        selected_task.processing_delay = comp_time
        selected_task.communication_delay = comm_time
        selected_task.total_delay = total_time
        selected_task.completion_time = current_time + total_time
        
        self.logger.info(f"Scheduled task {selected_task.task_id} for processing")
        return selected_task
    
    def _check_timeouts(self):
        """Check and drop timeout tasks"""
        current_time = 0.0  # Will be provided by environment
        timeout_tasks = [task for task in self.task_queue if task.deadline <= current_time]
        
        for task in timeout_tasks:
            self._drop_task(task, "timeout")
    
    def _drop_task(self, task: ComputeTask, reason: str):
        """Drop task and update statistics"""
        if task in self.task_queue:
            self.task_queue.remove(task)
        
        task.status = TaskStatus.DROPPED
        self.dropped_tasks.append(task)
        self.total_tasks_dropped += 1
        self.total_penalty += task.drop_penalty
        
        self.logger.info(f"Dropped task {task.task_id}, reason: {reason}")
    
    def _calculate_average_wait_time(self) -> float:
        """Calculate average queue waiting time"""
        if not self.task_queue:
            return 0.0
        
        current_time = 0.0  # Will be provided by environment
        total_wait = sum(current_time - task.arrival_time for task in self.task_queue)
        return total_wait / len(self.task_queue)
    
    def get_queue_status(self) -> Dict[str, Any]:
        """Get current queue status"""
        return {
            'queue_length': len(self.task_queue),
            'is_processing': self.is_processing,
            'processing_progress': self.processing_progress,
            'current_task_id': self.current_task.task_id if self.current_task else None,
            'battery_energy': self.battery_energy,
            'energy_percentage': (self.battery_energy / self.max_battery) * 100,
            'cpu_utilization': 1.0 if self.is_processing else 0.0
        }
    
    def get_energy_status(self) -> Dict[str, Any]:
        """Get energy status"""
        return {
            'battery_energy': self.battery_energy,
            'max_battery': self.max_battery,
            'energy_percentage': (self.battery_energy / self.max_battery) * 100,
            'total_energy_consumed': self.total_energy_consumed,
            'energy_threshold': self.max_battery * self.energy_threshold,
            'can_accept_tasks': self.battery_energy > (self.max_battery * self.energy_threshold)
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get comprehensive statistics"""
        return {
            'satellite_id': self.satellite_id,
            'total_tasks_processed': self.total_tasks_processed,
            'total_tasks_dropped': self.total_tasks_dropped,
            'total_penalty': self.total_penalty,
            'total_energy_consumed': self.total_energy_consumed,
            'queue_length': len(self.task_queue),
            'is_processing': self.is_processing,
            'processing_progress': self.processing_progress,
            'completed_tasks': len(self.completed_tasks),
            'dropped_tasks': len(self.dropped_tasks),
            'battery_energy': self.battery_energy,
            'current_bandwidth': self.current_bandwidth,
            'cpu_frequency': self.cpu_frequency
        }
    
    def reset(self):
        """Reset satellite state"""
        self.task_queue.clear()
        self.current_task = None
        self.completed_tasks.clear()
        self.dropped_tasks.clear()
        
        self.battery_energy = self.max_battery
        self.is_processing = False
        self.processing_progress = 0.0
        
        self.total_energy_consumed = 0.0
        self.total_tasks_processed = 0
        self.total_tasks_dropped = 0
        self.total_penalty = 0.0
        
        self.priority_cache.clear()
        
        self.logger.info(f"Reset satellite {self.satellite_id}")


# Alias for backward compatibility with existing code
Satellite = SatelliteCompute