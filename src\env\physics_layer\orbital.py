import pandas as pd
import numpy as np
import yaml
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple, List
import math
import os
import logging
from pathlib import Path

# Use absolute imports as per coding standard #1
from src.env.Foundation_Layer.time_manager import Time<PERSON>anager, TimeContext, create_time_manager_from_config


class Satellite:
    """Satellite class for storing basic satellite information"""
    def __init__(self, satellite_id: str, longitude: float, latitude: float, 
                 illuminated: bool, time_context: TimeContext, state: bool = True):
        self.satellite_id = satellite_id
        self.longitude = longitude
        self.latitude = latitude
        self.illuminated = illuminated
        self.time_context = time_context
        self.state = state  # TRUE means satellite is active/exists, FALSE means offline/doesn't exist
    
    @property
    def timestamp(self) -> datetime:
        """Backward compatible timestamp property"""
        return self.time_context.physical_time
    
    @property
    def simulation_step(self) -> int:
        """Current simulation step"""
        return self.time_context.simulation_step
        
    def __repr__(self):
        return f"Satellite({self.satellite_id}, lon={self.longitude:.3f}, lat={self.latitude:.3f})"


class GroundStation:
    """Ground station class for storing ground station information"""
    def __init__(self, station_id: str, longitude: float, latitude: float, 
                 name: str = None, station_type: str = None):
        self.station_id = station_id
        self.longitude = longitude
        self.latitude = latitude
        self.name = name
        self.station_type = station_type
        
    def __repr__(self):
        return f"GroundStation({self.station_id}, lon={self.longitude:.3f}, lat={self.latitude:.3f})"


class OrbitalUpdater:
    """Orbital update module - optimized version with unified time management"""

    def __init__(self, data_file: str = None,
                 config_file: str = None,
                 time_manager: Optional[TimeManager] = None):
        """Initialize orbital updater"""
        # Get the directory of the current file
        current_dir = Path(__file__).parent
        env_dir = current_dir.parent  # Go up one level to env directory

        # Set default paths relative to project structure
        self.data_file = data_file or str(env_dir / "env_data" / "satellite_data_72_0.csv")
        self.config_file = config_file or str(current_dir / "config.yaml")
        self.config = self._load_config()
        
        # Initialize time manager
        self.time_manager = time_manager or create_time_manager_from_config(self.config)
        
        # Load satellite data
        self.satellite_data = self._load_satellite_data()
        
        # Load ground stations and cloud centers
        self.ground_stations = self._load_ground_stations()
        self.cloud_stations = self._load_cloud_stations()
        
        # Performance optimization: cache mechanism
        self._satellites_cache = {}
        self._visibility_cache = {}
        
        # Get parameters from config
        self.earth_radius = self.config['system']['earth_radius_m'] / 1000.0
        self.satellite_altitude = self.config['system']['leo_altitude_m'] / 1000.0
        self.visibility_threshold = self.config['system']['visibility_threshold_m'] / 1000.0
        self.ground_visibility_threshold = self.config['system']['visibility_earth_m'] / 1000.0
        self.cloud_visibility_threshold = self.config['system']['cloud_visibility_threshold_m'] / 1000.0
        
    def _load_config(self) -> dict:
        """Load configuration file"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logging.critical(f"Failed to load config file: {self.config_file} - {e}")
            raise
            
    def _load_satellite_data(self) -> pd.DataFrame:
        """Load satellite data with new format"""
        try:
            df = pd.read_csv(self.data_file)
            
            # Rename columns to standard format
            column_mapping = {
                'satellite_ID': 'satellite_id',
                'lat': 'latitude', 
                'lon': 'longitude',
                'light': 'illuminated',
                # Support Chinese column names from new data file
                '卫星ID': 'satellite_id',
                '时隙': 'time_slot',
                '时间': 'time',
                '纬度': 'latitude',
                '经度': 'longitude', 
                '光照': 'illuminated',
                '状态': 'state'
            }
            df.rename(columns=column_mapping, inplace=True)
            
            # Convert boolean values
            df['illuminated'] = df['illuminated'].map({'TRUE': True, 'FALSE': False})
            
            # Convert state field to boolean (TRUE means satellite exists/is active)
            # Handle both string and boolean inputs
            if df['state'].dtype == 'object':
                # If state is string, map it
                df['state'] = df['state'].map({'TRUE': True, 'FALSE': False})
            elif df['state'].dtype == 'bool':
                # If already boolean, keep as is
                pass
            else:
                # For other types, convert to boolean
                df['state'] = df['state'].astype(bool)
            
            # Create proper timestamp from time_slot using config parameters
            # Use the same default as time_manager.py if not in config
            start_time_str = self.config['system'].get('simulation_start_time', '2025-06-08 04:00:00')
            base_time = datetime.strptime(start_time_str, '%Y-%m-%d %H:%M:%S')
            timeslot_duration = self.config['system']['timeslot_duration_s']
            df['timestamp'] = base_time + pd.to_timedelta(df['time_slot'] * timeslot_duration, unit='s')
            
            # Optimize with timestamp index
            df.set_index('timestamp', inplace=True)
            df.sort_index(inplace=True)
            
            self._unique_timestamps = sorted(df.index.unique())
            
            logging.info(f"Loaded {len(df)} satellite records with {len(self._unique_timestamps)} timestamps")
            return df
            
        except Exception as e:
            logging.error(f"Failed to load satellite data: {self.data_file} - {e}")
            raise RuntimeError(f"Failed to load satellite data: {e}") from e
    
    def _load_ground_stations(self) -> Dict[str, GroundStation]:
        """Load ground station data (420 ground user terminals)"""
        ground_stations = {}
        # Use dynamic path construction
        current_dir = Path(__file__).parent
        env_dir = current_dir.parent  # Go up one level to env directory
        ground_station_file = env_dir / "env_data" / "global_ground_stations.csv"

        if ground_station_file.exists():
            try:
                df = pd.read_csv(str(ground_station_file))
                for _, row in df.iterrows():
                    station = GroundStation(
                        station_id=str(row['ID']),
                        longitude=row['Longitude'],
                        latitude=row['Latitude'],
                        name=f"Station_{row['ID']}_{row['RegionType']}_{row['Size']}",
                        station_type=row['PurposeType']
                    )
                    ground_stations[station.station_id] = station
                logging.info(f"Successfully loaded {len(ground_stations)} ground stations")
            except Exception as e:
                logging.error(f"Failed to load ground station data: {e}")
                raise RuntimeError(f"Failed to load ground station data: {e}") from e
        else:
            logging.warning(f"Ground station data file not found: {ground_station_file}")
        
        return ground_stations
    
    def _load_cloud_stations(self) -> Dict[str, GroundStation]:
        """Load cloud center data"""
        cloud_stations = {}
        # Use dynamic path construction
        current_dir = Path(__file__).parent
        env_dir = current_dir.parent  # Go up one level to env directory
        cloud_station_file = env_dir / "env_data" / "cloud_station.csv"

        if cloud_station_file.exists():
            try:
                df = pd.read_csv(str(cloud_station_file))
                for _, row in df.iterrows():
                    station_id = f"cloud_{int(row['ID'])}"
                    cloud_station = GroundStation(
                        station_id=station_id,
                        longitude=row['Longitude'],
                        latitude=row['Latitude'],
                        name=f"Cloud Center {int(row['ID'])}",
                        station_type="cloud_center"
                    )
                    cloud_stations[station_id] = cloud_station
                logging.info(f"Successfully loaded {len(cloud_stations)} cloud centers")
            except Exception as e:
                logging.error(f"Failed to load cloud center data: {e}")
                raise RuntimeError(f"Failed to load cloud center data: {e}") from e
        else:
            logging.warning(f"Cloud center data file not found: {cloud_station_file}")
        
        return cloud_stations
    
    def get_satellites_at_time(self, time_step: int) -> Dict[str, Satellite]:
        """Get all satellite states at specified time step"""
        # Check cache
        if time_step in self._satellites_cache:
            return self._satellites_cache[time_step]
            
        if self.satellite_data.empty:
            return {}
            
        # Validate time step
        if not self.time_manager.is_valid_step(time_step):
            logging.warning(f"Time step {time_step} out of valid range")
            return {}
        
        # Get time context    
        time_context = self.time_manager.get_time_context(time_step)
        
        # Use cached timestamps for better performance (CLAUDE.md requirement)
        if not hasattr(self, '_unique_timestamps') or not self._unique_timestamps:
            self._unique_timestamps = sorted(self.satellite_data.index.unique())
        
        if time_step >= len(self._unique_timestamps):
            logging.warning(f"Time step {time_step} exceeds data range")
            return {}
            
        target_time = self._unique_timestamps[time_step]
        time_data = self.satellite_data.loc[target_time]
        
        satellites = {}
        if isinstance(time_data, pd.Series):
            time_data = time_data.to_frame().T
            
        for _, row in time_data.iterrows():
            # Only include satellites with state=TRUE (active/existing satellites)
            if row.get('state', True):  # Default to True if state column is missing
                satellite = Satellite(
                    satellite_id=row['satellite_id'],
                    longitude=row['longitude'],
                    latitude=row['latitude'],
                    illuminated=row['illuminated'],
                    time_context=time_context,
                    state=row.get('state', True)
                )
                satellites[row['satellite_id']] = satellite
            # Skip satellites with state=FALSE (they are considered as not existing)
        
        # Cache result
        self._satellites_cache[time_step] = satellites
        return satellites
    
    def _calculate_3d_distance(self, lat1: float, lon1: float, alt1: float,
                              lat2: float, lon2: float, alt2: float) -> float:
        """Calculate 3D distance between two points using ECEF coordinates"""
        lat1_rad = math.radians(lat1)
        lon1_rad = math.radians(lon1)
        lat2_rad = math.radians(lat2)
        lon2_rad = math.radians(lon2)
        
        # Convert to ECEF coordinates
        r1 = self.earth_radius + alt1
        r2 = self.earth_radius + alt2
        
        x1 = r1 * math.cos(lat1_rad) * math.cos(lon1_rad)
        y1 = r1 * math.cos(lat1_rad) * math.sin(lon1_rad)
        z1 = r1 * math.sin(lat1_rad)
        
        x2 = r2 * math.cos(lat2_rad) * math.cos(lon2_rad)
        y2 = r2 * math.cos(lat2_rad) * math.sin(lon2_rad)
        z2 = r2 * math.sin(lat2_rad)
        
        distance = math.sqrt((x2-x1)**2 + (y2-y1)**2 + (z2-z1)**2)
        return distance
    
    def calculate_distance(self, sat1: Satellite, sat2: Satellite) -> float:
        """Calculate 3D distance between two satellites"""
        return self._calculate_3d_distance(
            sat1.latitude, sat1.longitude, self.satellite_altitude,
            sat2.latitude, sat2.longitude, self.satellite_altitude
        )
    
    def calculate_visibility(self, sat1: Satellite, sat2: Satellite) -> bool:
        """Calculate visibility between two satellites based on distance"""
        if sat1.satellite_id == sat2.satellite_id:
            return False
            
        distance = self.calculate_distance(sat1, sat2)
        return distance <= self.visibility_threshold
    
    def build_visibility_matrix(self, satellites: Dict[str, Satellite]) -> Tuple[np.ndarray, np.ndarray]:
        """Build inter-satellite visibility matrix with distances using vectorized operations
        
        Returns:
            tuple: (visibility_matrix, distance_matrix) where:
                - visibility_matrix: boolean matrix indicating visibility
                - distance_matrix: float matrix containing distances in km
        """
        satellite_list = list(satellites.values())
        n = len(satellite_list)
        
        if n == 0:
            return np.array([], dtype=bool).reshape(0, 0), np.array([], dtype=float).reshape(0, 0)
        
        # Extract coordinates as numpy arrays for vectorized computation
        lats = np.array([math.radians(sat.latitude) for sat in satellite_list])
        lons = np.array([math.radians(sat.longitude) for sat in satellite_list])
        
        # Vectorized ECEF coordinate calculation
        r = self.earth_radius + self.satellite_altitude
        x = r * np.cos(lats) * np.cos(lons)
        y = r * np.cos(lats) * np.sin(lons)
        z = r * np.sin(lats)
        
        # Compute pairwise distances using broadcasting
        dx = x[:, np.newaxis] - x[np.newaxis, :]
        dy = y[:, np.newaxis] - y[np.newaxis, :]
        dz = z[:, np.newaxis] - z[np.newaxis, :]
        
        distances = np.sqrt(dx**2 + dy**2 + dz**2)
        
        # Create visibility matrix based on distance threshold
        visibility_matrix = (distances <= self.visibility_threshold) & (distances > 0)
        
        return visibility_matrix, distances
    
    def build_satellite_ground_visibility_matrix(self, satellites: Dict[str, Satellite], 
                                               time_step: int = None) -> Tuple[np.ndarray, np.ndarray]:
        """Build satellite-ground station visibility matrix with distances
        
        Returns:
            tuple: (visibility_matrix, distance_matrix) where:
                - visibility_matrix: boolean matrix (N_satellites x N_ground_stations)
                - distance_matrix: distance matrix in km (N_satellites x N_ground_stations)
        """
        cache_key = f"sat_ground_vis_{time_step}" if time_step is not None else None
        if cache_key and cache_key in self._visibility_cache:
            return self._visibility_cache[cache_key]
        
        satellite_list = list(satellites.values())
        ground_station_list = list(self.ground_stations.values())
        
        n_satellites = len(satellite_list)
        n_ground_stations = len(ground_station_list)
        
        if n_satellites == 0 or n_ground_stations == 0:
            empty_vis = np.array([], dtype=bool).reshape(n_satellites, n_ground_stations)
            empty_dist = np.array([], dtype=float).reshape(n_satellites, n_ground_stations)
            return empty_vis, empty_dist
        
        # Vectorized distance calculation
        sat_lats = np.array([math.radians(sat.latitude) for sat in satellite_list])
        sat_lons = np.array([math.radians(sat.longitude) for sat in satellite_list])
        ground_lats = np.array([math.radians(gs.latitude) for gs in ground_station_list])
        ground_lons = np.array([math.radians(gs.longitude) for gs in ground_station_list])
        
        # ECEF coordinates for satellites
        r_sat = self.earth_radius + self.satellite_altitude
        sat_x = r_sat * np.cos(sat_lats) * np.cos(sat_lons)
        sat_y = r_sat * np.cos(sat_lats) * np.sin(sat_lons)
        sat_z = r_sat * np.sin(sat_lats)
        
        # ECEF coordinates for ground stations
        r_ground = self.earth_radius
        ground_x = r_ground * np.cos(ground_lats) * np.cos(ground_lons)
        ground_y = r_ground * np.cos(ground_lats) * np.sin(ground_lons)
        ground_z = r_ground * np.sin(ground_lats)
        
        # Broadcasting to compute all pairwise distances
        dx = sat_x[:, np.newaxis] - ground_x[np.newaxis, :]
        dy = sat_y[:, np.newaxis] - ground_y[np.newaxis, :]
        dz = sat_z[:, np.newaxis] - ground_z[np.newaxis, :]
        
        distances = np.sqrt(dx**2 + dy**2 + dz**2)
        visibility_matrix = distances <= self.ground_visibility_threshold
        
        result = (visibility_matrix, distances)
        if cache_key:
            self._visibility_cache[cache_key] = result
        
        return result
    
    def build_satellite_cloud_visibility_matrix(self, satellites: Dict[str, Satellite], 
                                              time_step: int = None) -> Tuple[np.ndarray, np.ndarray]:
        """Build satellite-cloud center visibility matrix with distances
        
        Returns:
            tuple: (visibility_matrix, distance_matrix) where:
                - visibility_matrix: boolean matrix (N_satellites x N_cloud_stations)
                - distance_matrix: distance matrix in km (N_satellites x N_cloud_stations)
        """
        cache_key = f"sat_cloud_vis_{time_step}" if time_step is not None else None
        if cache_key and cache_key in self._visibility_cache:
            return self._visibility_cache[cache_key]
        
        satellite_list = list(satellites.values())
        cloud_station_list = list(self.cloud_stations.values())
        
        n_satellites = len(satellite_list)
        n_cloud_stations = len(cloud_station_list)
        
        if n_satellites == 0 or n_cloud_stations == 0:
            empty_vis = np.array([], dtype=bool).reshape(n_satellites, n_cloud_stations)
            empty_dist = np.array([], dtype=float).reshape(n_satellites, n_cloud_stations)
            return empty_vis, empty_dist
        
        # Vectorized distance calculation
        sat_lats = np.array([math.radians(sat.latitude) for sat in satellite_list])
        sat_lons = np.array([math.radians(sat.longitude) for sat in satellite_list])
        cloud_lats = np.array([math.radians(cs.latitude) for cs in cloud_station_list])
        cloud_lons = np.array([math.radians(cs.longitude) for cs in cloud_station_list])
        
        # ECEF coordinates for satellites
        r_sat = self.earth_radius + self.satellite_altitude
        sat_x = r_sat * np.cos(sat_lats) * np.cos(sat_lons)
        sat_y = r_sat * np.cos(sat_lats) * np.sin(sat_lons)
        sat_z = r_sat * np.sin(sat_lats)
        
        # ECEF coordinates for cloud stations
        r_cloud = self.earth_radius
        cloud_x = r_cloud * np.cos(cloud_lats) * np.cos(cloud_lons)
        cloud_y = r_cloud * np.cos(cloud_lats) * np.sin(cloud_lons)
        cloud_z = r_cloud * np.sin(cloud_lats)
        
        # Broadcasting to compute all pairwise distances
        dx = sat_x[:, np.newaxis] - cloud_x[np.newaxis, :]
        dy = sat_y[:, np.newaxis] - cloud_y[np.newaxis, :]
        dz = sat_z[:, np.newaxis] - cloud_z[np.newaxis, :]
        
        distances = np.sqrt(dx**2 + dy**2 + dz**2)
        visibility_matrix = distances <= self.cloud_visibility_threshold
        
        result = (visibility_matrix, distances)
        if cache_key:
            self._visibility_cache[cache_key] = result
        
        return result
    
    def get_total_timeslots(self) -> int:
        """Get total number of timeslots"""
        return self.time_manager.total_timeslots
    
    def get_satellite_ids(self, time_step: int) -> List[str]:
        """Get list of satellite IDs at given time step"""
        satellites = self.get_satellites_at_time(time_step)
        return list(satellites.keys())
    
    def get_ground_station_count(self) -> int:
        """Get number of ground stations"""
        return len(self.ground_stations)
    
    def get_cloud_station_count(self) -> int:
        """Get number of cloud stations"""
        return len(self.cloud_stations)