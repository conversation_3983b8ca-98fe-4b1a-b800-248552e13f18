"""
卫星任务处理模块测试
按时隙输出测试结果，验证DPSQ调度算法和任务处理功能
"""

import sys
import os
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List
import time

# 添加项目根目录到sys.path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# 使用绝对导入，注意文件夹名称包含&符号
import importlib.util
satellite_module_path = project_root / "src" / "env" / "satellite&cloud" / "satellite.py"
spec = importlib.util.spec_from_file_location("satellite", satellite_module_path)
satellite_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(satellite_module)

Satellite = satellite_module.Satellite
SatelliteTask = satellite_module.SatelliteTask
DPSQScheduler = satellite_module.DPSQScheduler
TaskStatus = satellite_module.TaskStatus

from src.env.physics_layer.orbital import OrbitalUpdater
from src.env.physics_layer.communication_refactored import CommunicationManager
from src.env.physics_layer.task_tracking import TaskTrackingRecord, TaskStatus as TrackingStatus


def create_test_tasks(num_tasks: int, current_time: float, config: Dict) -> List[SatelliteTask]:
    """创建测试任务"""
    tasks = []
    np.random.seed(42)  # 可重复性
    
    for i in range(num_tasks):
        # 使用配置中的真实参数
        task = SatelliteTask(
            task_id=f"test_task_{i}",
            priority=np.random.randint(1, 6),  # 优先级1-5
            deadline=current_time + np.random.uniform(20, 100),  # 20-100秒后截止
            data_size=np.random.uniform(10, 50),  # 10-50 MB
            complexity=np.random.uniform(1e9, 1e10),  # 1e9-1e10 CPU cycles
            drop_penalty=np.random.uniform(50, 200),  # 丢弃惩罚
            arrival_time=current_time
        )
        tasks.append(task)
    
    return tasks


def test_single_timeslot(satellite: Satellite, time_step: int, timeslot_duration: float):
    """测试单个时隙"""
    current_time = time_step * timeslot_duration  # 转换为秒
    
    print(f"\n{'='*80}")
    print(f"时隙 {time_step} (时间: {current_time:.1f}秒)")
    print(f"{'='*80}")
    
    # 1. 生成新任务
    if time_step % 5 == 0:  # 每5个时隙生成一批任务
        num_new_tasks = np.random.poisson(3)  # 泊松分布
        new_tasks = create_test_tasks(num_new_tasks, current_time, satellite.config)
        for task in new_tasks:
            satellite.add_task(task)
        print(f"生成 {num_new_tasks} 个新任务")
    
    # 2. 更新带宽（从通信管理器获取真实值）
    if satellite.comm_manager:
        # 假设连接到地面站1
        bandwidth = satellite.update_bandwidth_from_communication(time_step, 1)
        print(f"当前带宽: {bandwidth:.2f} MB/s")
    
    # 3. 调度任务
    scheduled = satellite.schedule_tasks(current_time)
    print(f"调度 {len(scheduled)} 个任务:")
    for task in scheduled:
        print(f"  - {task.task_id}: 优先级={task.priority}, "
              f"截止时间={task.deadline:.1f}s, CPU分配={task.allocated_cpu:.2%}")
    
    # 4. 处理任务
    completed = satellite.process_tasks(current_time)
    print(f"完成 {len(completed)} 个任务:")
    for task in completed:
        print(f"  - {task.task_id}: 总处理时间={task.accumulated_processing_time:.2f}s, "
              f"能耗={task.accumulated_energy:.2e}J")
    
    # 5. 输出统计信息
    stats = satellite.get_statistics()
    print(f"\n当前状态:")
    print(f"  队列长度: {stats['queue_length']}")
    print(f"  处理中任务: {stats['processing_tasks']}")
    print(f"  已完成任务: {stats['completed_tasks']}")
    print(f"  已丢弃任务: {stats['dropped_tasks']}")
    print(f"  可用CPU: {stats['available_cpu']:.2%}")
    print(f"  总能耗: {stats['total_energy_consumed']:.2e}J")
    print(f"  总惩罚: {stats['total_penalty']:.1f}")
    
    return stats


def test_dpsq_scheduler():
    """测试DPSQ调度器"""
    print("\n" + "="*80)
    print("测试DPSQ调度器")
    print("="*80)
    
    # 加载配置
    config_path = project_root / "src" / "env" / "physics_layer" / "config.yaml"
    import yaml
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 创建调度器
    scheduler = DPSQScheduler(config)
    
    # 创建测试任务
    current_time = 100.0  # 秒
    task = SatelliteTask(
        task_id="test_scheduler_task",
        priority=3,
        deadline=150.0,  # 50秒后截止
        data_size=30.0,  # 30MB
        complexity=5e9,  # 5e9 cycles
        drop_penalty=100.0,
        arrival_time=current_time
    )
    
    # 测试优先级分数计算
    bandwidth = 50.0  # MB/s
    score = scheduler.calculate_priority_score(task, current_time, bandwidth)
    print(f"任务优先级分数: {score:.4f}")
    
    # 测试处理时间估算
    cpu_allocation = 0.5
    total_time, comm_time, comp_time = scheduler.estimate_processing_time(
        task, cpu_allocation, bandwidth
    )
    print(f"预计处理时间:")
    print(f"  通信时间: {comm_time:.2f}秒")
    print(f"  计算时间: {comp_time:.2f}秒")
    print(f"  总时间: {total_time:.2f}秒")
    
    # 测试可行性检查
    feasible = scheduler.check_feasibility(task, current_time, cpu_allocation, bandwidth)
    print(f"任务可行性: {'可行' if feasible else '不可行'}")


def test_cross_timeslot_processing():
    """测试跨时隙任务处理"""
    print("\n" + "="*80)
    print("测试跨时隙任务处理")
    print("="*80)
    
    # 加载配置
    config_path = project_root / "src" / "env" / "physics_layer" / "config.yaml"
    
    # 创建卫星（ID=1）
    orbital = OrbitalUpdater(str(config_path))
    comm = CommunicationManager(orbital, str(config_path))
    satellite = Satellite(1, str(config_path), orbital, comm)
    
    # 创建一个大任务（需要多个时隙才能完成）
    large_task = SatelliteTask(
        task_id="large_cross_timeslot_task",
        priority=5,  # 高优先级
        deadline=200.0,  # 200秒后截止
        data_size=100.0,  # 100MB
        complexity=1e11,  # 1e11 cycles（需要较长时间）
        drop_penalty=500.0,
        arrival_time=0.0
    )
    
    satellite.add_task(large_task)
    
    # 模拟多个时隙处理
    timeslot_duration = 4.0  # 每个时隙4秒
    for time_step in range(10):
        current_time = time_step * timeslot_duration
        
        print(f"\n时隙 {time_step} (时间: {current_time:.1f}秒)")
        
        # 调度和处理
        if time_step == 0:
            scheduled = satellite.schedule_tasks(current_time)
            print(f"开始处理任务: {large_task.task_id}")
        
        # 模拟时隙边界
        satellite.handle_timeslot_boundary(current_time + timeslot_duration)
        
        # 处理任务
        completed = satellite.process_tasks(current_time + timeslot_duration)
        
        # 检查任务状态
        if large_task in satellite.current_tasks:
            print(f"  进度: {large_task.processing_progress:.1%}")
            print(f"  剩余复杂度: {large_task.remaining_complexity:.2e}")
            print(f"  累计处理时间: {large_task.accumulated_processing_time:.2f}秒")
            print(f"  累计能耗: {large_task.accumulated_energy:.2e}J")
        
        if completed:
            print(f"任务完成！总耗时: {large_task.accumulated_processing_time:.2f}秒")
            break


def main():
    """主测试函数"""
    print("="*80)
    print("卫星任务处理模块测试")
    print("="*80)
    
    # 1. 测试DPSQ调度器
    test_dpsq_scheduler()
    
    # 2. 测试跨时隙处理
    test_cross_timeslot_processing()
    
    # 3. 测试完整的多时隙仿真
    print("\n" + "="*80)
    print("多时隙完整仿真测试")
    print("="*80)
    
    # 加载配置
    config_path = project_root / "src" / "env" / "physics_layer" / "config.yaml"
    
    # 创建卫星系统
    orbital = OrbitalUpdater(str(config_path))
    comm = CommunicationManager(orbital, str(config_path))
    satellite = Satellite(1, str(config_path), orbital, comm)
    
    # 运行10个时隙
    timeslot_duration = 4.0  # 秒
    results = []
    
    for time_step in range(10):
        stats = test_single_timeslot(satellite, time_step, timeslot_duration)
        stats['time_step'] = time_step
        stats['time_seconds'] = time_step * timeslot_duration
        results.append(stats)
    
    # 输出汇总统计
    print("\n" + "="*80)
    print("仿真汇总统计")
    print("="*80)
    
    df = pd.DataFrame(results)
    print("\n时隙统计:")
    print(df[['time_step', 'queue_length', 'processing_tasks', 
              'completed_tasks', 'dropped_tasks']].to_string(index=False))
    
    print(f"\n总体性能:")
    print(f"  总完成任务: {satellite.total_tasks_processed}")
    print(f"  总丢弃任务: {satellite.total_tasks_dropped}")
    print(f"  总能耗: {satellite.total_energy_consumed:.2e}J")
    print(f"  总惩罚: {satellite.total_penalty:.1f}")
    print(f"  完成率: {satellite.total_tasks_processed / max(1, satellite.total_tasks_processed + satellite.total_tasks_dropped):.1%}")
    
    # 保存结果到CSV
    output_file = project_root / "test" / "env" / "satellite&cloud" / "satellite_test_results.csv"
    output_file.parent.mkdir(parents=True, exist_ok=True)
    df.to_csv(output_file, index=False)
    print(f"\n测试结果已保存到: {output_file}")


if __name__ == "__main__":
    main()