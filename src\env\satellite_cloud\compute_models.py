"""
Compute models for satellite and cloud computing
Defines data structures for compute resource management
"""

from dataclasses import dataclass, field
from typing import List, Dict, Optional, Any
from enum import Enum
import numpy as np

# Absolute imports as per coding standard
from src.env.physics_layer.task_models import Task


class TaskStatus(Enum):
    """Task status enumeration"""
    PENDING = "pending"          # Waiting in queue
    PROCESSING = "processing"    # Being processed
    COMPLETED = "completed"      # Completed successfully
    DROPPED = "dropped"          # Dropped due to constraints
    FAILED = "failed"           # Failed to process


class NodeType(Enum):
    """Processing node type"""
    SATELLITE = "satellite"
    CLOUD = "cloud"


@dataclass
class ComputeTask:
    """
    Task representation for compute nodes
    Extended from Task model with compute-specific fields
    """
    # Core task data
    task_id: int
    priority: float              # Static priority P_i
    deadline: float             # Absolute deadline D_i (seconds)
    data_size_mb: float         # Data size S_i (MB)
    complexity: float           # Computational complexity C_i (CPU cycles)
    drop_penalty: float         # Drop penalty W_i
    arrival_time: float         # Arrival time (seconds)
    
    # Processing state
    start_time: Optional[float] = None
    completion_time: Optional[float] = None
    status: TaskStatus = TaskStatus.PENDING
    allocated_cpu: float = 0.0              # Allocated CPU ratio (0-1)
    
    # Performance tracking
    energy_consumed: float = 0.0            # Energy consumed (J)
    processing_delay: float = 0.0           # Processing delay (s)
    communication_delay: float = 0.0        # Communication delay (s)
    total_delay: float = 0.0                # Total delay (s)
    
    # Cross-timeslot processing support
    processing_progress: float = 0.0         # Processing progress (0-1)
    remaining_complexity: float = 0.0        # Remaining complexity (CPU cycles)
    accumulated_processing_time: float = 0.0 # Accumulated processing time (s)
    accumulated_energy: float = 0.0          # Accumulated energy (J)
    last_update_time: Optional[float] = None # Last update time (s)
    
    # Task atomicity - no segmentation allowed
    is_atomic: bool = True                   # Tasks are atomic and indivisible
    
    def __post_init__(self):
        """Initialize remaining complexity"""
        if self.remaining_complexity == 0.0:
            self.remaining_complexity = self.complexity
    
    def __lt__(self, other):
        """For priority queue comparison"""
        return self.task_id < other.task_id
    
    @classmethod
    def from_task(cls, task: Task, arrival_time: float, drop_penalty: float = 100.0) -> 'ComputeTask':
        """
        Create ComputeTask from Task model
        
        Args:
            task: Original Task object
            arrival_time: Arrival time in seconds
            drop_penalty: Penalty for dropping task
            
        Returns:
            ComputeTask object
        """
        return cls(
            task_id=task.task_id,
            priority=float(task.priority),
            deadline=float(task.deadline_timestamp),
            data_size_mb=task.data_size_mb,
            complexity=task.total_complexity,  # Use total_complexity property
            drop_penalty=drop_penalty,
            arrival_time=arrival_time
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'task_id': self.task_id,
            'priority': self.priority,
            'deadline': self.deadline,
            'data_size_mb': self.data_size_mb,
            'complexity': self.complexity,
            'status': self.status.value,
            'processing_progress': self.processing_progress,
            'energy_consumed': self.energy_consumed,
            'total_delay': self.total_delay
        }


@dataclass
class ProcessingNode:
    """Processing node record for task execution tracking"""
    node_type: NodeType
    node_id: int
    processing_percentage: float
    start_time: float
    end_time: float
    
    # Performance metrics
    processing_time: float = 0.0
    energy_consumption: float = 0.0
    cpu_cycles_used: float = 0.0
    memory_usage_mb: float = 0.0
    
    # Communication metrics (if applicable)
    offload_transmission_time: float = 0.0
    offload_bandwidth_usage: float = 0.0
    result_transmission_time: float = 0.0
    result_size_mb: float = 0.0


@dataclass
class ComputeNodeState:
    """Base class for compute node state"""
    node_id: int
    node_type: NodeType
    max_queue_size: int
    current_queue_length: int
    is_available: bool
    utilization_rate: float


@dataclass
class SatelliteState(ComputeNodeState):
    """Satellite compute state"""
    # Energy state
    battery_energy: float       # Current battery energy (J)
    max_battery: float         # Maximum battery capacity (J)
    illuminated: bool          # Whether in sunlight
    energy_consumption_rate: float  # Energy consumption rate (W)
    
    # Processing state
    cpu_frequency: float       # CPU frequency (Hz)
    available_cpu: float       # Available CPU ratio (0-1)
    task_queue: List[ComputeTask] = field(default_factory=list)
    current_tasks: List[ComputeTask] = field(default_factory=list)
    
    # Performance tracking
    total_tasks_processed: int = 0
    total_tasks_dropped: int = 0
    total_energy_consumed: float = 0.0
    total_penalty: float = 0.0
    
    def __post_init__(self):
        """Set node type to satellite"""
        self.node_type = NodeType.SATELLITE


@dataclass
class CloudState(ComputeNodeState):
    """Cloud compute state"""
    # Processing capabilities
    cpu_frequency: float       # CPU frequency (Hz) - much higher than satellite
    max_parallel_tasks: int    # Maximum parallel tasks
    current_parallel_count: int  # Current parallel task count
    
    # Resource state
    total_capacity: float      # Total compute capacity
    available_capacity: float # Available compute capacity
    task_queue: List[ComputeTask] = field(default_factory=list)
    processing_tasks: List[ComputeTask] = field(default_factory=list)
    
    # Performance tracking
    total_tasks_processed: int = 0
    total_processing_time: float = 0.0
    
    def __post_init__(self):
        """Set node type to cloud"""
        self.node_type = NodeType.CLOUD


@dataclass
class ProcessingResult:
    """Result of task processing operation"""
    completed_tasks: List[ComputeTask] = field(default_factory=list)
    failed_tasks: List[ComputeTask] = field(default_factory=list)
    dropped_tasks: List[ComputeTask] = field(default_factory=list)
    
    # Performance metrics
    energy_consumed: float = 0.0
    processing_time: float = 0.0
    queue_wait_time: float = 0.0
    cpu_utilization: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'completed_count': len(self.completed_tasks),
            'failed_count': len(self.failed_tasks),
            'dropped_count': len(self.dropped_tasks),
            'energy_consumed': self.energy_consumed,
            'processing_time': self.processing_time,
            'queue_wait_time': self.queue_wait_time,
            'cpu_utilization': self.cpu_utilization
        }


@dataclass
class ResourceMetrics:
    """System-wide resource utilization metrics"""
    satellite_metrics: Dict[int, SatelliteState] = field(default_factory=dict)
    cloud_metrics: Dict[int, CloudState] = field(default_factory=dict)
    
    # Aggregate metrics
    total_energy_consumed: float = 0.0
    average_queue_length: float = 0.0
    system_utilization: float = 0.0
    task_completion_rate: float = 0.0
    
    # Performance statistics
    total_tasks_completed: int = 0
    total_tasks_failed: int = 0
    total_tasks_dropped: int = 0
    average_processing_delay: float = 0.0
    
    def calculate_system_metrics(self):
        """Calculate aggregate system metrics"""
        total_satellites = len(self.satellite_metrics)
        total_clouds = len(self.cloud_metrics)
        
        if total_satellites > 0:
            # Calculate satellite averages
            total_queue = sum(s.current_queue_length for s in self.satellite_metrics.values())
            self.average_queue_length = total_queue / total_satellites
            
            total_util = sum(s.utilization_rate for s in self.satellite_metrics.values())
            sat_util = total_util / total_satellites
            
            self.total_energy_consumed = sum(s.total_energy_consumed 
                                           for s in self.satellite_metrics.values())
        else:
            sat_util = 0.0
        
        if total_clouds > 0:
            # Calculate cloud averages
            cloud_util = sum(c.utilization_rate for c in self.cloud_metrics.values())
            cloud_util = cloud_util / total_clouds
        else:
            cloud_util = 0.0
        
        # System utilization is weighted average
        total_nodes = total_satellites + total_clouds
        if total_nodes > 0:
            self.system_utilization = (sat_util * total_satellites + 
                                     cloud_util * total_clouds) / total_nodes
        
        # Task completion rate
        total_processed = self.total_tasks_completed + self.total_tasks_failed + self.total_tasks_dropped
        if total_processed > 0:
            self.task_completion_rate = self.total_tasks_completed / total_processed
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'total_energy_consumed': self.total_energy_consumed,
            'average_queue_length': self.average_queue_length,
            'system_utilization': self.system_utilization,
            'task_completion_rate': self.task_completion_rate,
            'total_tasks_completed': self.total_tasks_completed,
            'total_tasks_failed': self.total_tasks_failed,
            'total_tasks_dropped': self.total_tasks_dropped,
            'average_processing_delay': self.average_processing_delay,
            'num_satellites': len(self.satellite_metrics),
            'num_clouds': len(self.cloud_metrics)
        }