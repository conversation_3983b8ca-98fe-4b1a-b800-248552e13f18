2025-08-16 15:35:19 [   ERROR] root: Error in get_global_state: Object of type int64 is not JSON serializable
2025-08-16 15:35:19 [   ERROR] root: Traceback (most recent call last):
  File "D:\paper\space\SPACE2\src\env\visualization\api_server.py", line 100, in get_global_state
    return jsonify(result)
  File "C:\Users\<USER>\.conda\envs\space\lib\site-packages\flask\json\__init__.py", line 170, in jsonify
    return current_app.json.response(*args, **kwargs)  # type: ignore[return-value]
  File "C:\Users\<USER>\.conda\envs\space\lib\site-packages\flask\json\provider.py", line 214, in response
    f"{self.dumps(obj, **dump_args)}\n", mimetype=self.mimetype
  File "C:\Users\<USER>\.conda\envs\space\lib\site-packages\flask\json\provider.py", line 179, in dumps
    return json.dumps(obj, **kwargs)
  File "C:\Users\<USER>\.conda\envs\space\lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
  File "C:\Users\<USER>\.conda\envs\space\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\.conda\envs\space\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\.conda\envs\space\lib\site-packages\flask\json\provider.py", line 121, in _default
    raise TypeError(f"Object of type {type(o).__name__} is not JSON serializable")
TypeError: Object of type int64 is not JSON serializable

